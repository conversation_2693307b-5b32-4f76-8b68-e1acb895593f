from scrapers.base_scraper import BaseScraper
import logging
import re
from urllib.parse import urljoin, urlparse, parse_qs, urlencode, urlunparse
import json

class GlassdoorScraper(BaseScraper):
    def __init__(self, config, db_manager, processor):
        super().__init__(config, db_manager, processor)
        self.site_name = "Glassdoor"
        self.seen_jobs = set()  # Track seen jobs to prevent duplicates
        
    def extract_job_listings(self, soup, site_name):
        """Extract job listings using Glassdoor-specific patterns"""
        job_listings = []
        
        # Glassdoor-specific selectors (try multiple patterns)
        job_containers = []

        # Try various Glassdoor job container selectors
        selectors_to_try = [
            'div[data-test="jobListing"]',
            'li[data-adv-type="GENERAL"]',
            'div.react-job-listing',
            'div.jobContainer',
            'article[data-test="jobListing"]',
            'div[data-test="job-listing"]',
            'li[data-test="jobListing"]',
            'div.job-search-card',
            'div[class*="job"]',
            'li[class*="job"]',
            'article[class*="job"]',
            'div[data-jl]',
            'div[data-job-id]',
            'div[id*="job"]',
            'li[id*="job"]'
        ]

        for selector in selectors_to_try:
            containers = soup.select(selector)
            if containers:
                job_containers = containers
                logging.info(f"Found {len(containers)} Glassdoor job containers using selector: {selector}")
                break
        
        if not job_containers:
            logging.warning("No Glassdoor job containers found with local patterns, falling back to AI")
            return self._fallback_to_ai(soup, site_name)
            
        logging.info(f"Found {len(job_containers)} Glassdoor job containers using local patterns")
        
        new_jobs = []
        duplicate_count = 0
        
        for i, container in enumerate(job_containers):
            try:
                job = self._extract_job_from_container(container, soup)
                logging.debug(f"Container {i+1}: title='{job.get('title', '')}', company='{job.get('company', '')}', url='{job.get('url', '')}'")

                if job and job.get('title') and job.get('url'):
                    # Create job signature for duplicate detection
                    job_signature = f"{job['title']}|{job['company']}"

                    if job_signature not in self.seen_jobs:
                        self.seen_jobs.add(job_signature)
                        new_jobs.append(job)
                        logging.info(f"Added Glassdoor job: {job['title']} at {job['company']}")
                    else:
                        duplicate_count += 1
                        logging.debug(f"Duplicate job: {job['title']} at {job['company']}")
                else:
                    logging.warning(f"Job validation failed - title: '{job.get('title', '')}', url: '{job.get('url', '')}'")

            except Exception as e:
                logging.error(f"Error extracting Glassdoor job: {e}")
        
        # Log duplicate detection results
        if duplicate_count > 0:
            logging.info(f"Glassdoor: Found {duplicate_count} duplicate jobs, {len(new_jobs)} new jobs")
            
        logging.info(f"Extracted {len(new_jobs)} jobs from Glassdoor using local patterns")
        return new_jobs
    
    def _extract_job_from_container(self, container, soup):
        """Extract job data from Glassdoor container using local patterns"""
        job = {}
        
        # Extract title
        title_elem = (container.select_one('[data-test="job-title"] a') or 
                     container.select_one('.jobTitle a') or
                     container.select_one('h2 a') or
                     container.select_one('a[data-test="job-title"]'))
        job['title'] = title_elem.get_text(strip=True) if title_elem else ""
        
        # Extract company (updated selectors based on actual HTML structure)
        company_elem = (container.select_one('span.EmployerProfile_compactEmployerName__9MGcV') or
                       container.select_one('[data-test="employer-name"]') or
                       container.select_one('.employerName') or
                       container.select_one('.companyName') or
                       container.select_one('[data-test="employer-short-name"]'))
        job['company'] = company_elem.get_text(strip=True) if company_elem else ""

        # Extract location (updated selectors based on actual HTML structure)
        location_elem = (container.select_one('div.JobCard_location__Ds1fM[data-test="emp-location"]') or
                        container.select_one('[data-test="job-location"]') or
                        container.select_one('.location') or
                        container.select_one('.jobLocation'))
        job['location'] = location_elem.get_text(strip=True) if location_elem else ""
        
        # Extract URL (updated selectors based on actual HTML structure)
        url_elem = (container.select_one('a[data-test="job-title"]') or  # This is the correct selector!
                   container.select_one('[data-test="job-title"] a') or
                   container.select_one('.jobTitle a') or
                   container.select_one('h2 a'))
        if url_elem and url_elem.get('href'):
            job['url'] = urljoin('https://www.glassdoor.com', url_elem.get('href'))
        else:
            job['url'] = ""
            
        # Extract description/summary (updated selectors based on actual HTML structure)
        desc_elem = (container.select_one('div.JobCard_jobDescriptionSnippet__l1tnl[data-test="descSnippet"]') or
                    container.select_one('[data-test="job-description"]') or
                    container.select_one('.jobDescription') or
                    container.select_one('.summary'))
        job['description'] = desc_elem.get_text(strip=True) if desc_elem else ""

        # Extract salary if available (updated selectors based on actual HTML structure)
        salary_elem = (container.select_one('div.JobCard_salaryEstimate__QpbTW[data-test="detailSalary"]') or
                      container.select_one('[data-test="detailSalary"]') or
                      container.select_one('.salary') or
                      container.select_one('.salaryText'))
        job['salary'] = salary_elem.get_text(strip=True) if salary_elem else ""
        
        # Extract rating if available
        rating_elem = container.select_one('[data-test="rating"]')
        job['rating'] = rating_elem.get_text(strip=True) if rating_elem else ""
        
        # Set source site
        job['source_site'] = 'Glassdoor'
        
        # Extract city from location
        job['city'] = self._extract_city_from_location(job['location'])
        
        return job
    
    def extract_next_page(self, soup, current_url):
        """Extract next page URL using Glassdoor-specific patterns with duplicate detection"""
        # Check if we've seen too many duplicates (indicates pagination loop)
        if hasattr(self, '_consecutive_duplicate_pages'):
            if self._consecutive_duplicate_pages >= 2:
                logging.info("Glassdoor: Stopping pagination due to too many duplicate pages")
                return None
        else:
            self._consecutive_duplicate_pages = 0
            
        # Try local patterns first
        next_page_url = self._extract_next_page_local(soup, current_url)
        
        if next_page_url:
            logging.info(f"Found next page using local patterns: {next_page_url}")
            return next_page_url
            
        # Fallback to AI if local patterns fail
        logging.info("Local pagination patterns failed, falling back to AI")
        return self._fallback_pagination_to_ai(soup, current_url)
    
    def _extract_next_page_local(self, soup, current_url):
        """Extract next page using Glassdoor-specific local patterns"""
        # Method 1: Look for "Next" button
        next_link = (soup.select_one('a[data-test="pagination-next"]') or 
                    soup.select_one('a[aria-label="Next"]') or
                    soup.select_one('.nextButton a') or
                    soup.select_one('a.next'))
                    
        if next_link and next_link.get('href'):
            return urljoin(current_url, next_link.get('href'))
            
        # Method 2: Look for pagination with page numbers
        pagination_links = soup.select('[data-test="pagination"] a, .pagination a')
        
        for link in pagination_links:
            link_text = link.get_text(strip=True).lower()
            if any(word in link_text for word in ['next', '>', '›']):
                return urljoin(current_url, link.get('href'))
                
        # Method 3: Construct next page URL from current URL
        parsed_url = urlparse(current_url)
        query_params = parse_qs(parsed_url.query)
        
        # Glassdoor uses 'p' parameter for pagination
        if 'p' in query_params:
            current_page = int(query_params['p'][0])
            next_page = current_page + 1
        else:
            # First pagination - add p=2
            next_page = 2
            
        # Update page parameter
        query_params['p'] = [str(next_page)]
        
        # Reconstruct URL
        new_query = urlencode(query_params, doseq=True)
        next_url = urlunparse((
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            new_query,
            parsed_url.fragment
        ))
        
        # Limit to reasonable number of pages (Glassdoor often has pagination loops)
        if next_page <= 5:  # Limit to 5 pages max for Glassdoor
            return next_url
            
        return None
    
    def _extract_city_from_location(self, location):
        """Extract city from location string"""
        if not location:
            return ""
            
        # Common patterns: "City, State", "City (State)", "Remote in City"
        location_parts = location.split(',')
        if len(location_parts) > 0:
            city = location_parts[0].strip()
            # Remove "Remote" or similar prefixes
            city = re.sub(r'^(Remote|Remote in|Hybrid|Work from home)\s*', '', city, flags=re.IGNORECASE)
            return city.strip()
            
        return ""
    
    def reset_seen_jobs(self):
        """Reset seen jobs tracker (call this when starting a new scraping session)"""
        self.seen_jobs.clear()
        self._consecutive_duplicate_pages = 0
    
    def _fallback_to_ai(self, soup, site_name):
        """Fallback to AI extraction when local patterns fail"""
        from scrapers.ai_scraper import AIScraper
        ai_scraper = AIScraper(self.config, self.db_manager, self.processor)
        return ai_scraper.extract_job_listings(soup, site_name)
    
    def _fallback_pagination_to_ai(self, soup, current_url):
        """Fallback to AI pagination when local patterns fail"""
        from scrapers.ai_scraper import AIScraper
        ai_scraper = AIScraper(self.config, self.db_manager, self.processor)
        return ai_scraper.extract_next_page(soup, current_url)
