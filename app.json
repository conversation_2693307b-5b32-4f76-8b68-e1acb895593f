{"name": "HubScannerV2", "description": "Advanced job scraper with hybrid AI/local pattern extraction", "keywords": ["python", "scraping", "jobs", "ai"], "website": "https://github.com/112hub/hubscannerv2", "repository": "https://github.com/112hub/hubscannerv2", "stack": "heroku-22", "buildpacks": [{"url": "heroku/python"}], "env": {"MYSQL_HOST": {"description": "MySQL database host", "required": true}, "MYSQL_USER": {"description": "MySQL database user", "required": true}, "MYSQL_PASSWORD": {"description": "MySQL database password", "required": true}, "MYSQL_DATABASE": {"description": "MySQL database name", "required": true}, "SCRAPFLY_API_KEY": {"description": "Scrapfly API key for web scraping", "required": true}, "OPENAI_API_KEY": {"description": "OpenAI API key for AI-powered extraction", "required": true}, "SENDGRID_API_KEY": {"description": "SendGrid API key for email notifications", "required": true}, "EMAIL_RECIPIENT": {"description": "Email address to receive reports", "value": "<EMAIL>"}, "EMAIL_SENDER": {"description": "Email address to send from", "value": "<EMAIL>"}}, "formation": {"worker": {"quantity": 0, "size": "basic"}}, "addons": [{"plan": "scheduler:standard"}]}