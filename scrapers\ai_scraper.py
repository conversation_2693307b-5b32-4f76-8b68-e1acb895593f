from scrapers.base_scraper import <PERSON><PERSON><PERSON>raper
import logging
import json
from openai import OpenAI
from bs4 import BeautifulSoup

class AIScraper(BaseScraper):
    def __init__(self, config, db_manager, processor):
        super().__init__(config, db_manager, processor)
        self.site_name = "Generic"
        self.openai_client = OpenAI(api_key=config['openai']['api_key'])

    def extract_job_listings(self, soup, site_name, seen_jobs=None):
        """Extract job listings using AI"""
        self.site_name = site_name
        if seen_jobs is None:
            seen_jobs = set()

        # Pre-process HTML to focus on likely job listing containers
        job_containers = self._extract_potential_job_containers(soup)

        # If we found potential job containers, use those instead of the full HTML
        if job_containers:
            html_content = str(job_containers)
            logging.info(f"Using {len(job_containers)} potential job containers for {site_name}")
        else:
            # Clean and optimize HTML before sending to AI
            cleaned_html = self._clean_html_for_ai(soup, site_name)
            html_content = str(cleaned_html)
            logging.info(f"Using cleaned full HTML for {site_name} (no job containers identified)")

        # Check if HTML is still too large after cleaning
        max_content_length = 300000  # Increased limit since we're cleaning instead of truncating
        if len(html_content) > max_content_length:
            # If still too large, apply more aggressive cleaning
            logging.warning(f"HTML still large after cleaning ({len(html_content)} chars), applying aggressive cleaning")
            aggressively_cleaned_html = self._aggressive_clean_html_for_ai(html_content, site_name)
            html_content = str(aggressively_cleaned_html)

            # Only truncate as last resort
            if len(html_content) > max_content_length:
                truncated_html = html_content[:max_content_length]
                logging.warning(f"HTML truncated as last resort from {len(html_content)} to {max_content_length} characters")
            else:
                truncated_html = html_content
                logging.info(f"Aggressive cleaning successful: {len(html_content)} characters")
        else:
            truncated_html = html_content
            logging.info(f"HTML cleaning successful: {len(html_content)} characters")

        # Create system prompt for job extraction
        system_prompt = f"""
        You are an expert web scraper. Extract job listings from the HTML content provided from {site_name}.

        IMPORTANT: Look for job listings in these common patterns:
        - For StepStone: Look for <article> elements with data-testid="job-item" or class="res-4em2ed"
        - For Indeed: Look for <div> elements with class="job_seen_beacon"
        - For OCC: Look for <div> elements with id starting with "jobcard-" (e.g., id="jobcard-20490426")
        - For Glassdoor: Look for <div> elements with data-test="jobListing"
        - For Talenteca: Look for <div> elements with class="job-item"
        - For other sites: Look for elements with job-related classes or data attributes

        For each job listing, extract ALL available information from the HTML data:

        IMPORTANT: Look carefully at ALL text content within each job container. Extract:

        BASIC FIELDS:
        - Job title (usually in h2, h3, or elements with "title" in class)
        - Company name (usually in elements with "company" in class, or for OCC look for company names in span elements)
        - Location (usually in elements with "location" in class, or for OCC look for city/state in span elements)
        - Job URL (complete URL, not relative - look for href attributes. For OCC, include the jobcard ID from the container)
        - Description (extract any available description/summary from the listing)

        SPECIAL INSTRUCTIONS FOR OCC:
        - Job containers have IDs like "jobcard-20490426" - extract this ID number
        - Company names appear in span elements, often truncated with "..."
        - Locations appear as multiple span elements like "Guadalajara, Jal."
        - Job URLs should be constructed as: https://www.occ.com.mx/empleo/[JOB_ID] where JOB_ID is the number from jobcard-XXXXXX
        - Include the jobcard ID in the response so URLs can be generated
        - Look for job titles in h2 elements within the job containers
        - Extract salary information from span elements containing "Sueldo" or "$"
        - Look for experience requirements in text containing "años" or "experiencia"

        ENHANCED FIELDS (look for these in the HTML text content):
        - Job type (look for "full-time", "part-time", "contract", "freelance", "temporary", "permanent", etc.)
        - Experience level (look for "junior", "senior", "lead", "principal", "entry", "mid", "experienced", years of experience like "3+ years", etc.)
        - Skills/technologies (look for programming languages, frameworks, tools: "Python", "Java", "React", "AWS", "Docker", etc.)
        - Remote status (look for "remote", "hybrid", "on-site", "home office", "flexible", "office", etc.)

        EXTRACTION STRATEGY:
        - Read ALL text content in each job container, not just specific elements
        - Look for keywords and patterns in job titles, descriptions, and metadata
        - Extract skills from job titles (e.g., "Python Developer" → skills: ["Python"])
        - Infer experience level from titles (e.g., "Senior" → experience_level: "senior")
        - Look for remote indicators in location or description text

        Extract as much information as possible from what's visible on the listing page.
        Return the data as a JSON object with a "jobs" key containing an array of job objects.
        If you cannot find any job listings, return an empty array for "jobs".
        Do not make up information. If a field is not available, use null or an empty string.

        Example response format:
        {{
          "jobs": [
            {{
              "title": "Senior Python Developer",
              "company": "TechCorp GmbH",
              "location": "Berlin, Germany",
              "url": "https://example.com/jobs/123",
              "description": "We are looking for an experienced Python developer to join our team. You will work with Django, PostgreSQL, and AWS...",
              "job_type": "full-time",
              "experience_level": "senior",
              "skills": ["Python", "Django", "PostgreSQL", "AWS", "Docker"],
              "remote_status": "hybrid",
              "jobcard_id": null
            }},
            {{
              "title": "Ingeniero de Software",
              "company": "TESSELAR SOLUCIONES SA",
              "location": "Guadalajara, Jal.",
              "url": "",
              "description": "Únete a una de las mejores empresas para trabajar en México...",
              "job_type": "full-time",
              "experience_level": "mid",
              "skills": ["Software Engineering"],
              "remote_status": "on-site",
              "jobcard_id": "20490426"
            }}
          ]
        }}
        """

        try:
            # Call OpenAI API to extract job listings
            response = self.openai_client.chat.completions.create(
                model="o3-mini",  # Use o3-mini model
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Extract job listings from this HTML content from {site_name}:\n\n{truncated_html}"}
                ],
                response_format={"type": "json_object"}
            )

            # Parse the response
            try:
                result = json.loads(response.choices[0].message.content)
                extracted_jobs = result.get("jobs", [])

                # Process each extracted job
                job_listings = []
                for job_data in extracted_jobs:
                    job = {
                        'title': job_data.get('title', 'Unknown Title'),
                        'company': job_data.get('company', 'Unknown Company'),
                        'location': job_data.get('location', ''),
                        'description': job_data.get('description', ''),
                        'url': job_data.get('url', ''),

                        'source_site': site_name,
                        'city': self._extract_city_from_location(job_data.get('location', '')),

                        # Enhanced fields from listing page
                        'job_type': job_data.get('job_type'),
                        'experience_level': job_data.get('experience_level'),
                        'remote_status': job_data.get('remote_status'),
                        'skills': self._process_skills(job_data.get('skills', []))
                    }

                    # Handle OCC-specific URL generation
                    if not job['url'] and site_name == 'OCC':
                        # For OCC, use jobcard_id to generate URL
                        jobcard_id = job_data.get('jobcard_id')
                        if jobcard_id:
                            job['url'] = f"https://www.occ.com.mx/empleo/{jobcard_id}"

                    # Only add jobs with valid URLs
                    if job['url']:
                        job_listings.append(job)

                logging.info(f"Extracted {len(job_listings)} jobs from {site_name}")
                return job_listings

            except json.JSONDecodeError as e:
                logging.error(f"Failed to parse OpenAI response as JSON: {e}")
                logging.debug(f"Raw response: {response.choices[0].message.content}")
                return []

        except Exception as e:
            logging.error(f"Error using AI to parse jobs from {site_name}: {e}")
            return []

    def _extract_potential_job_containers(self, soup):
        """Extract potential job listing containers to reduce HTML size"""
        containers = []

        # Common job listing container selectors
        selectors = [
            # Generic selectors
            'div.job-listing', 'div.job-card', 'div.job-result', 'div.job-box',
            'li.job-listing', 'article.job', 'div[data-job-id]', 'div.job-row',
            'div.vacancy', 'div.position', '.job', '.listing', '.job-search-card',
            'div.job_seen_beacon', 'article.job-element', 'div.jobsearch-ResultsList',
            'ul.jobs-list', 'div.jobs-container', 'div.results', 'div.search-results',

            # StepStone-specific selectors
            'article[data-testid="job-item"]',
            'div[data-testid="job-card-content"]',
            'article.res-4em2ed',
            'article[data-jobcard-green-phase]',
            'div.job-results-row',

            # Indeed-specific selectors (already working)
            'div.job_seen_beacon',

            # OCC-specific selectors
            'div[id^="jobcard-"]',

            # Glassdoor-specific selectors
            'div[data-test="jobListing"]', 'div.react-job-listing', 'li[data-adv-type="GENERAL"]',
            'article[data-test="jobListing"]', 'div[data-test="job-listing"]', 'li[data-test="jobListing"]',
            'div.job-search-card', 'div[class*="job"]', 'li[class*="job"]', 'article[class*="job"]',
            'div[data-jl]', 'div[data-job-id]', 'div[id*="job"]', 'li[id*="job"]',

            # Talenteca-specific selectors
            'div.job-item', 'div.job-listing-item'
        ]

        for selector in selectors:
            elements = soup.select(selector)
            if elements and len(elements) > 2:  # At least 3 elements to be considered a job listing container
                containers.extend(elements)
                logging.info(f"Found {len(elements)} potential job containers with selector: {selector}")

        return containers

    def _extract_city_from_location(self, location):
        """Extract city from location string"""
        if not location:
            return ""

        # Try to extract city from location
        # Common patterns: "City, State", "City (State)", "Remote in City"
        location_parts = location.split(',')
        if len(location_parts) > 0:
            city = location_parts[0].strip()
            # Remove "Remote in" or similar prefixes
            city = city.replace("Remote in", "").strip()
            return city

        return ""

    def _process_skills(self, skills):
        """Process skills array into JSON string for database storage"""
        if not skills:
            return '[]'

        # If it's already a list, convert to JSON string
        if isinstance(skills, list):
            return json.dumps(skills)

        # If it's a string, try to parse it or return as single item
        if isinstance(skills, str):
            try:
                # Try to parse as JSON
                parsed = json.loads(skills)
                return json.dumps(parsed)
            except:
                # Treat as comma-separated string
                skill_list = [skill.strip() for skill in skills.split(',') if skill.strip()]
                return json.dumps(skill_list)

        return '[]'

    def _parse_posted_date(self, date_str):
        """Parse various date formats into MySQL DATE format (YYYY-MM-DD)"""
        if not date_str:
            return None

        date_str = str(date_str).strip().lower()

        # Handle relative dates like "4 hours ago", "2 days ago", etc.
        import re
        from datetime import datetime, timedelta

        # Pattern for "X hours ago", "X days ago", etc.
        relative_pattern = r'(\d+)\s+(hour|day|week|month)s?\s+ago'
        match = re.search(relative_pattern, date_str)

        if match:
            number = int(match.group(1))
            unit = match.group(2)

            now = datetime.now()
            if unit == 'hour':
                target_date = now - timedelta(hours=number)
            elif unit == 'day':
                target_date = now - timedelta(days=number)
            elif unit == 'week':
                target_date = now - timedelta(weeks=number)
            elif unit == 'month':
                target_date = now - timedelta(days=number * 30)  # Approximate
            else:
                return None

            return target_date.strftime('%Y-%m-%d')

        # Handle "today", "yesterday"
        if 'today' in date_str:
            return datetime.now().strftime('%Y-%m-%d')
        elif 'yesterday' in date_str:
            return (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')

        # Try to parse standard date formats
        try:
            # Try common date formats
            for fmt in ['%Y-%m-%d', '%d.%m.%Y', '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d %H:%M:%S']:
                try:
                    parsed_date = datetime.strptime(date_str, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue
        except:
            pass

        # If all parsing fails, return None
        return None

    def extract_next_page(self, soup, current_url):
        """Extract next page URL using AI"""
        html_content = str(soup)

        # Truncate HTML content if too large
        max_content_length = 75000  # Increased from 50000
        truncated_html = html_content[:max_content_length] if len(html_content) > max_content_length else html_content

        # Create system prompt for next page extraction
        system_prompt = """
        You are an expert web scraper. Analyze the HTML content and find the URL for the next page of job listings.

        IMPORTANT: Look for these pagination patterns:

        1. TRADITIONAL PAGINATION:
        - Links with "Next", "Siguiente", ">" text
        - Numbered page links (2, 3, 4, etc.)
        - Elements with href containing "page=", "p=", "offset=", "start="

        2. OCC-SPECIFIC PATTERNS:
        - Look for URLs with parameters like "?page=2", "?p=2", "?offset=20"
        - Check for buttons or links with "Cargar más", "Ver más", "Load more"
        - Look for pagination in script tags or data attributes
        - Check for URLs that modify the current URL by adding page parameters

        3. STEPSTONE/GLASSDOOR PATTERNS:
        - Look for "paging_next", "IP2", "IP3" in URLs
        - Check for pagination navigation elements

        4. GENERAL PATTERNS:
        - Any link that appears to be the next page in sequence
        - Elements with data-page, data-next attributes
        - JavaScript-generated pagination URLs

        CONSTRUCTION RULES:
        - If you find a relative URL, make it absolute using the current domain
        - For OCC: If current URL has "?tm=1&sort=2", next page should add "&page=2" or similar
        - Ensure the URL maintains existing parameters while adding pagination

        Return ONLY the full URL to the next page as a JSON object with a "next_page_url" key.
        If there is no next page or you cannot find a next page link, return null for the "next_page_url" value.

        Example responses:
        {
          "next_page_url": "https://www.occ.com.mx/empleos/de-ingeniero-de-software/en-jalisco/?tm=1&sort=2&page=2"
        }

        {
          "next_page_url": "https://www.stepstone.de/work/software-engineer/in-cologne?radius=30&page=2&sort=2"
        }

        or if no next page:
        {
          "next_page_url": null
        }
        """

        try:
            # Call OpenAI API to extract next page URL
            response = self.openai_client.chat.completions.create(
                model="o3-mini",  # Use o3-mini model
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Find the next page URL from this HTML content. Current URL is: {current_url}\n\n{truncated_html}"}
                ],
                response_format={"type": "json_object"}
            )

            # Parse the response
            try:
                result = json.loads(response.choices[0].message.content)
                next_page_url = result.get("next_page_url")

                if next_page_url:
                    logging.info(f"Found next page URL: {next_page_url}")
                    return next_page_url
                else:
                    logging.info("No next page found")
                    return None

            except json.JSONDecodeError as e:
                logging.error(f"Failed to parse OpenAI response as JSON: {e}")
                return None

        except Exception as e:
            logging.error(f"Error using AI to find next page: {e}")
            return None

    def _clean_html_for_ai(self, soup, site_name):
        """Clean HTML by removing unnecessary elements before sending to AI"""
        # Create a copy to avoid modifying the original
        cleaned_soup = soup.__copy__()

        # Remove elements that are not useful for job extraction
        elements_to_remove = [
            'script',           # JavaScript code
            'style',            # CSS styles
            'noscript',         # No-script content
            'iframe',           # Embedded frames
            'svg',              # SVG graphics
            'img',              # Images (keep alt text though)
            'video',            # Videos
            'audio',            # Audio
            'canvas',           # Canvas elements
            'object',           # Object embeds
            'embed',            # Embedded content
            'form',             # Forms (usually not job content)
            'input',            # Form inputs
            'button[type="submit"]',  # Submit buttons
            'nav',              # Navigation (usually not job content)
            'footer',           # Footer content
            'aside',            # Sidebar content
            'header',           # Header content (sometimes)
            '[class*="cookie"]', # Cookie banners
            '[class*="banner"]', # Advertisement banners
            '[class*="popup"]',  # Popup elements
            '[class*="modal"]',  # Modal dialogs
            '[class*="overlay"]', # Overlay elements
            '[id*="cookie"]',    # Cookie elements
            '[id*="banner"]',    # Banner elements
            '[data-test*="cookie"]', # Cookie test elements
        ]

        # Site-specific cleaning
        if 'glassdoor' in site_name.lower():
            elements_to_remove.extend([
                '[data-test="sign-in"]',
                '[data-test="header"]',
                '[data-test="footer"]',
                '[class*="header"]',
                '[class*="footer"]',
                '[class*="navigation"]',
                '[class*="sidebar"]',
                '[class*="filter"]',
                '[class*="sort"]',
            ])
        elif 'indeed' in site_name.lower():
            elements_to_remove.extend([
                '[data-testid="header"]',
                '[data-testid="footer"]',
                '[data-testid="filter"]',
                '.np',  # Indeed navigation
                '.pn',  # Indeed pagination (we handle separately)
            ])
        elif 'stepstone' in site_name.lower():
            elements_to_remove.extend([
                '[data-testid="header"]',
                '[data-testid="footer"]',
                '[data-testid="filter"]',
                '[data-testid="sort"]',
            ])
        elif 'occ' in site_name.lower():
            elements_to_remove.extend([
                '.header',
                '.footer',
                '.navigation',
                '.filters',
                '.sidebar',
            ])

        # Remove the elements
        for selector in elements_to_remove:
            try:
                for element in cleaned_soup.select(selector):
                    element.decompose()
            except Exception:
                # Ignore selector errors
                pass

        # Remove empty elements and whitespace-only elements
        for element in cleaned_soup.find_all():
            if not element.get_text(strip=True) and not element.find_all():
                element.decompose()

        # Remove comments
        from bs4 import Comment
        for comment in cleaned_soup.find_all(string=lambda text: isinstance(text, Comment)):
            comment.extract()

        logging.info(f"HTML cleaned for {site_name}: removed unnecessary elements")
        return cleaned_soup

    def _aggressive_clean_html_for_ai(self, html_content, _site_name):
        """Apply more aggressive cleaning when HTML is still too large"""
        from bs4 import BeautifulSoup

        soup = BeautifulSoup(html_content, 'html.parser')

        # Remove more elements aggressively
        elements_to_remove = [
            'nav', 'footer', 'aside', 'header',
            'form', 'input', 'button', 'select', 'textarea',
            'iframe', 'embed', 'object', 'video', 'audio',
            'svg', 'canvas', 'map', 'area',
            '[class*="cookie"]', '[class*="banner"]', '[class*="popup"]',
            '[class*="modal"]', '[class*="overlay"]', '[class*="sidebar"]',
            '[class*="menu"]', '[class*="navigation"]', '[class*="breadcrumb"]',
            '[class*="filter"]', '[class*="sort"]', '[class*="pagination"]',
            '[class*="footer"]', '[class*="header"]', '[class*="ad"]',
            '[class*="advertisement"]', '[class*="promo"]', '[class*="social"]',
            '[data-testid*="filter"]', '[data-testid*="sort"]',
            '[data-testid*="pagination"]', '[data-testid*="navigation"]'
        ]

        for selector in elements_to_remove:
            for element in soup.select(selector):
                element.decompose()

        # Remove elements with minimal text content
        for element in soup.find_all(['div', 'span', 'p']):
            text = element.get_text(strip=True)
            if len(text) < 10 and not element.find(['a', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
                element.decompose()

        # Remove excessive whitespace and empty elements
        html_str = str(soup)
        import re
        html_str = re.sub(r'\s+', ' ', html_str)  # Replace multiple whitespace with single space
        html_str = re.sub(r'>\s+<', '><', html_str)  # Remove whitespace between tags

        return BeautifulSoup(html_str, 'html.parser')


