#!/bin/bash

# HubScannerV2 Heroku Deployment Script
# This script helps deploy HubScannerV2 to Heroku with proper configuration

set -e

echo "🚀 HubScannerV2 Heroku Deployment Script"
echo "========================================"

# Check if Heroku CLI is installed
if ! command -v heroku &> /dev/null; then
    echo "❌ Heroku CLI is not installed. Please install it first:"
    echo "   https://devcenter.heroku.com/articles/heroku-cli"
    exit 1
fi

# Check if user is logged in to Heroku
if ! heroku auth:whoami &> /dev/null; then
    echo "❌ Please log in to Heroku first:"
    echo "   heroku login"
    exit 1
fi

# Get app name
read -p "Enter your Heroku app name: " APP_NAME

if [ -z "$APP_NAME" ]; then
    echo "❌ App name cannot be empty"
    exit 1
fi

echo "📱 Creating Heroku app: $APP_NAME"
heroku create $APP_NAME || echo "App might already exist, continuing..."

echo "🔧 Setting up environment variables..."

# Database configuration
read -p "MySQL Host: " MYSQL_HOST
read -p "MySQL User: " MYSQL_USER
read -s -p "MySQL Password: " MYSQL_PASSWORD
echo
read -p "MySQL Database: " MYSQL_DATABASE

# API Keys
read -s -p "Scrapfly API Key: " SCRAPFLY_API_KEY
echo
read -s -p "OpenAI API Key: " OPENAI_API_KEY
echo
read -s -p "SendGrid API Key: " SENDGRID_API_KEY
echo

# Email settings
read -p "Email Sender (default: <EMAIL>): " EMAIL_SENDER
EMAIL_SENDER=${EMAIL_SENDER:-<EMAIL>}

read -p "Email Recipient (default: <EMAIL>): " EMAIL_RECIPIENT
EMAIL_RECIPIENT=${EMAIL_RECIPIENT:-<EMAIL>}

echo "🔑 Setting environment variables on Heroku..."

heroku config:set \
    MYSQL_HOST="$MYSQL_HOST" \
    MYSQL_USER="$MYSQL_USER" \
    MYSQL_PASSWORD="$MYSQL_PASSWORD" \
    MYSQL_DATABASE="$MYSQL_DATABASE" \
    SCRAPFLY_API_KEY="$SCRAPFLY_API_KEY" \
    OPENAI_API_KEY="$OPENAI_API_KEY" \
    SENDGRID_API_KEY="$SENDGRID_API_KEY" \
    EMAIL_SENDER="$EMAIL_SENDER" \
    EMAIL_RECIPIENT="$EMAIL_RECIPIENT" \
    --app $APP_NAME

echo "📅 Adding Heroku Scheduler addon..."
heroku addons:create scheduler:standard --app $APP_NAME

echo "🚀 Deploying to Heroku..."
git add .
git commit -m "Deploy HubScannerV2 to Heroku" || echo "No changes to commit"
git push heroku main

echo "✅ Deployment complete!"
echo ""
echo "📋 Next steps:"
echo "1. Configure scheduled jobs:"
echo "   heroku addons:open scheduler --app $APP_NAME"
echo ""
echo "2. Add these scheduled jobs:"
echo "   - Morning run: 'python main.py' at 08:00 UTC"
echo "   - Evening run: 'python main.py' at 20:00 UTC"
echo ""
echo "3. Monitor logs:"
echo "   heroku logs --tail --app $APP_NAME"
echo ""
echo "4. Test the deployment:"
echo "   heroku run python main.py 1 --app $APP_NAME"
echo ""
echo "🎉 HubScannerV2 is now deployed on Heroku!"
