import mysql.connector
from mysql.connector import Error
import logging
from scrapfly import ScrapeConfig, ScrapflyClient
from bs4 import BeautifulSoup
import time
import re
import json

class DatabaseManager:
    def __init__(self, config):
        self.config = config
        self.connection = None
        self.connect()
        
    def connect(self):
        try:
            self.connection = mysql.connector.connect(
                host=self.config['mysql']['host'],
                user=self.config['mysql']['user'],
                password=self.config['mysql']['password'],
                database=self.config['mysql']['database']
            )
            if self.connection.is_connected():
                logging.info("Connected to MySQL database")
        except Error as e:
            logging.error(f"Error connecting to MySQL: {e}")
    
    def get_scraping_links(self):
        """Get all links to scrape from the database"""
        try:
            cursor = self.connection.cursor(dictionary=True)
            query = "SELECT id, url, site_name, city FROM scraping_links WHERE active = 1"
            cursor.execute(query)
            links = cursor.fetchall()
            cursor.close()
            return links
        except Error as e:
            logging.error(f"Error fetching links: {e}")
            return []
    
    def save_job(self, job_data):
        """Save a job to the database - always insert, no duplicate checking"""
        try:
            # Ensure all required fields are present
            job_data['city'] = job_data.get('city', '')
            job_data['posted_date'] = job_data.get('posted_date', None)

            cursor = self.connection.cursor()

            # Always insert new job - no duplicate checking during scraping
            insert_query = """
            INSERT INTO jobs (title, company, location, city, description, url, posted_date, source_site,
                            skills, experience_level, job_type, remote_status)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_query, (
                job_data['title'],
                job_data['company'],
                job_data['location'],
                job_data['city'],
                job_data['description'],
                job_data['url'],
                job_data.get('posted_date'),
                job_data['source_site'],
                job_data.get('skills'),
                job_data.get('experience_level'),
                job_data.get('job_type'),
                job_data.get('remote_status')
            ))
            logging.info(f"Added job: {job_data['title']} at {job_data['company']}")

            self.connection.commit()
            cursor.close()
        except Exception as e:
            logging.error(f"Error saving job: {e}")
    
    def get_jobs_count(self):
        """Get total count of jobs in database"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM jobs")
            result = cursor.fetchone()
            cursor.close()
            return result[0] if result else 0
        except Exception as e:
            logging.error(f"Error getting jobs count: {e}")
            return 0

    def close(self):
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logging.info("MySQL connection closed")

class GenericScraper:
    def __init__(self, config, db_manager, processor):
        self.config = config
        self.db_manager = db_manager
        self.processor = processor
        self.scrapfly = ScrapflyClient(key=config['scrapfly']['api_key'])
        
    async def scrape_page(self, url, site_name, city):
        """Scrape a single page and extract job listings using generic patterns"""
        try:
            result = await self.scrapfly.async_scrape(ScrapeConfig(
                url=url,
                asp=True,
                country="US"
            ))
            
            if result.status_code == 200:
                soup = BeautifulSoup(result.content, 'html.parser')
                
                # Extract job listings using generic patterns
                job_listings = self.extract_job_listings(soup, site_name, city)
                
                # Process and save each job
                for job in job_listings:
                    processed_job = self.processor.process_job(job)
                    self.db_manager.save_job(processed_job)
                
                # Find next page link if it exists
                next_page = self.extract_next_page(soup, url)
                return next_page
            else:
                logging.error(f"Failed to scrape {url}: Status code {result.status_code}")
                return None
        except Exception as e:
            logging.error(f"Error scraping {url}: {e}")
            return None
    
    def extract_job_listings(self, soup, site_name, city):
        """Extract job listings using generic patterns that work across many job sites"""
        job_listings = []
        
        # Common job listing containers
        job_containers = []
        
        # Try different common patterns for job listings
        selectors = [
            'div.job-listing', 'div.job-card', 'div.job-result', 'div.job-box',
            'li.job-listing', 'article.job', 'div[data-job-id]', 'div.job-row',
            'div.vacancy', 'div.position', '.job', '.listing'
        ]
        
        for selector in selectors:
            containers = soup.select(selector)
            if containers:
                job_containers = containers
                logging.info(f"Found {len(containers)} job listings using selector: {selector}")
                break
        
        # If no containers found, try to find elements with job-related attributes
        if not job_containers:
            all_divs = soup.find_all('div')
            job_containers = [div for div in all_divs if self._looks_like_job_container(div)]
            logging.info(f"Found {len(job_containers)} potential job containers using attribute analysis")
        
        # Process each container
        for container in job_containers:
            try:
                job = {
                    'title': self._extract_text(container, ['h2', 'h3', '.title', '.job-title']),
                    'company': self._extract_text(container, ['.company', '.employer', '.organization']),
                    'location': self._extract_text(container, ['.location', '.job-location', '.place']),
                    'city': city,  # Use the city from the scraping link
                    'description': self._extract_description(container),
                    'url': self._extract_url(container, soup),
                    'posted_date': self._extract_date(container),
                    'source_site': site_name
                }
                
                # Only add if we have at least a title and URL
                if job['title'] and job['url']:
                    job_listings.append(job)
            except Exception as e:
                logging.error(f"Error extracting job data: {e}")
        
        logging.info(f"Extracted {len(job_listings)} job listings")
        return job_listings
    
    def _looks_like_job_container(self, element):
        """Check if an element looks like a job container based on its attributes and content"""
        # Check element classes and IDs
        element_str = str(element).lower()
        job_keywords = ['job', 'position', 'vacancy', 'listing', 'career', 'opening']
        
        # Check if any job keywords are in the element's attributes
        for keyword in job_keywords:
            if keyword in element_str:
                return True
        
        # Check if it has typical job elements inside
        if element.find(['h2', 'h3', 'h4']) and (
            element.find(class_=lambda c: c and any(k in c.lower() for k in ['title', 'company', 'location'])) or
            element.find(string=lambda s: s and any(k in s.lower() for k in ['apply', 'posted', 'full-time', 'part-time']))
        ):
            return True
            
        return False
    
    def _extract_text(self, container, selectors):
        """Extract text using multiple possible selectors"""
        for selector in selectors:
            element = container.select_one(selector)
            if element:
                return element.get_text(strip=True)
            
        # If selectors don't work, try to find elements with matching text in class or id
        keywords = [s.replace('.', '') for s in selectors]
        for element in container.find_all(['span', 'div', 'p', 'h2', 'h3', 'h4']):
            element_classes = element.get('class', [])
            element_id = element.get('id', '')
            
            for keyword in keywords:
                if any(keyword in cls.lower() for cls in element_classes) or keyword in element_id.lower():
                    return element.get_text(strip=True)
        
        return ""
    
    def _extract_description(self, container):
        """Extract job description"""
        # Try common description selectors
        selectors = ['.description', '.summary', '.job-description', '.details']
        for selector in selectors:
            element = container.select_one(selector)
            if element:
                return element.get_text(strip=True)
        
        # If no description in the container, it might be loaded separately
        # Just return a brief summary from the container
        paragraphs = container.find_all('p')
        if paragraphs:
            return paragraphs[0].get_text(strip=True)
        
        return ""
    
    def _extract_url(self, container, soup):
        """Extract job URL"""
        # Try to find a link in the container
        link = container.find('a')
        if link and link.get('href'):
            href = link.get('href')
            # Handle relative URLs
            if href.startswith('/'):
                base_url = soup.find('base')
                if base_url and base_url.get('href'):
                    return base_url.get('href').rstrip('/') + href
                else:
                    # Try to extract domain from page
                    canonical = soup.find('link', {'rel': 'canonical'})
                    if canonical and canonical.get('href'):
                        domain = re.match(r'https?://[^/]+', canonical.get('href'))
                        if domain:
                            return domain.group(0) + href
            return href
        
        # Try to find a data attribute with URL
        for attr in container.attrs:
            if 'url' in attr.lower() or 'link' in attr.lower() or 'href' in attr.lower():
                return container[attr]
        
        # Look for apply button
        apply_button = container.find('a', string=re.compile(r'apply|view|details', re.I))
        if apply_button and apply_button.get('href'):
            return apply_button.get('href')
            
        return ""
    
    def _extract_date(self, container):
        """Extract posted date"""
        # Try common date selectors
        selectors = ['.date', '.posted-date', '.job-date', '.timestamp']
        for selector in selectors:
            element = container.select_one(selector)
            if element:
                return element.get_text(strip=True)
        
        # Look for text that looks like a date
        date_patterns = [
            r'\d{1,2}/\d{1,2}/\d{2,4}',  # MM/DD/YYYY
            r'\d{1,2}-\d{1,2}-\d{2,4}',  # MM-DD-YYYY
            r'\w+ \d{1,2}, \d{4}',       # Month DD, YYYY
            r'\d{1,2} \w+ \d{4}'         # DD Month YYYY
        ]
        
        for element in container.find_all(['span', 'div', 'p', 'time']):
            text = element.get_text(strip=True)
            for pattern in date_patterns:
                match = re.search(pattern, text)
                if match:
                    return match.group(0)
        
        return ""
    
    def extract_next_page(self, soup, current_url):
        """Extract next page URL using generic patterns"""
        # Common next page patterns
        next_selectors = [
            'a.next', 'a.pagination-next', 'a[rel="next"]', 
            'a:contains("Next")', 'a:contains("»")', 'a.next-page',
            '.pagination a:contains("Next")', '.pagination a:contains(">")'
        ]
        
        for selector in next_selectors:
            if ':contains' in selector:
                # BeautifulSoup doesn't support :contains, so handle separately
                text = selector.split(':contains(')[1].strip('"\')')
                elements = soup.find_all('a')
                for element in elements:
                    if text in element.get_text() and element.get('href'):
                        href = element.get('href')
                        # Handle relative URLs
                        if href.startswith('/'):
                            base_url = re.match(r'https?://[^/]+', current_url)
                            if base_url:
                                return base_url.group(0) + href
                        return href
            else:
                element = soup.select_one(selector)
                if element and element.get('href'):
                    href = element.get('href')
                    # Handle relative URLs
                    if href.startswith('/'):
                        base_url = re.match(r'https?://[^/]+', current_url)
                        if base_url:
                            return base_url.group(0) + href
                    return href
        
        # Try to find pagination with a current page and next page
        pagination = soup.select('.pagination a, .pager a, .pages a')
        if pagination:
            current_page_element = soup.select_one('.pagination .current, .pager .current, .pages .current')
            if current_page_element:
                current_page = int(current_page_element.get_text(strip=True))
                for page_link in pagination:
                    try:
                        page_num = int(page_link.get_text(strip=True))
                        if page_num == current_page + 1:
                            return page_link.get('href')
                    except ValueError:
                        continue
        
        # Look for URL patterns with page numbers
        page_match = re.search(r'[?&]page=(\d+)', current_url)
        if page_match:
            current_page = int(page_match.group(1))
            next_page = current_page + 1
            return current_url.replace(f'page={current_page}', f'page={next_page}')
        
        return None
    
    async def run_scraper(self, link):
        """Run the scraper for a specific link"""
        url = link['url']
        site_name = link['site_name']
        city = link['city']
        logging.info(f"Starting scraping for {site_name} in {city}: {url}")
        
        while url:
            logging.info(f"Scraping page: {url}")
            next_page = await self.scrape_page(url, site_name, city)
            
            if next_page == url:
                logging.warning(f"Next page is the same as current page: {url}")
                break
                
            url = next_page
            if url:
                # Add a delay between requests to avoid overloading the server
                time.sleep(self.config['scraping']['delay_seconds'])
        
        logging.info(f"Completed scraping for {site_name} in {city}")


