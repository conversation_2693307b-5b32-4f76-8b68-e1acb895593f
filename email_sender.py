#!/usr/bin/env python3
"""
Email sender module for HubScannerV2
Sends summary reports after scraping runs
"""
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from datetime import datetime
import logging
import yaml
import os
from dotenv import load_dotenv

class EmailSender:
    def __init__(self, config=None):
        # Load config if not provided
        if config is None:
            config = self._load_config()

        # Load email configuration
        email_config = config.get('email', {})

        # Override with environment variables if available
        load_dotenv()
        sendgrid_api_key = os.getenv('SENDGRID_API_KEY', email_config.get('sendgrid_api_key'))

        self.smtp_config = {
            'host': email_config.get('smtp_host', 'smtp.sendgrid.net'),
            'port': email_config.get('smtp_port', 587),
            'secure': False,
            'auth': {
                'user': 'apikey',
                'pass': sendgrid_api_key
            }
        }
        self.recipient = email_config.get('recipient', '<EMAIL>')
        self.sender = email_config.get('sender', '<EMAIL>')

    def _load_config(self):
        """Load configuration from config.yaml"""
        try:
            with open('config/config.yaml', 'r') as file:
                return yaml.safe_load(file)
        except Exception as e:
            logging.error(f"Failed to load config: {e}")
            return {}
    
    def send_summary_email(self, summary_data):
        """Send a summary email with scraping results"""
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = f"HubScannerV2 - Scraping Summary {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            msg['From'] = self.sender
            msg['To'] = self.recipient
            
            # Create HTML content
            html_content = self._create_html_summary(summary_data)
            
            # Create text content (fallback)
            text_content = self._create_text_summary(summary_data)
            
            # Attach parts
            part1 = MIMEText(text_content, 'plain')
            part2 = MIMEText(html_content, 'html')
            
            msg.attach(part1)
            msg.attach(part2)
            
            # Send email
            with smtplib.SMTP(self.smtp_config['host'], self.smtp_config['port']) as server:
                server.starttls()
                server.login(
                    self.smtp_config['auth']['user'], 
                    self.smtp_config['auth']['pass']
                )
                server.send_message(msg)
            
            logging.info(f"Summary email sent successfully to {self.recipient}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to send summary email: {e}")
            return False
    
    def _create_html_summary(self, data):
        """Create HTML email content"""
        mode = "🧪 TEST MODE" if data.get('test_mode') else "🚀 FULL MODE"
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .summary {{ background-color: #e8f5e9; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
                .site-results {{ margin-bottom: 20px; }}
                .site-header {{ background-color: #2196F3; color: white; padding: 10px; border-radius: 4px; }}
                .job-list {{ background-color: #f5f5f5; padding: 10px; margin-top: 10px; border-radius: 4px; }}
                .cleanup {{ background-color: #fff3e0; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
                .footer {{ color: #666; font-size: 12px; margin-top: 30px; }}
                .success {{ color: #4caf50; font-weight: bold; }}
                .warning {{ color: #ff9800; font-weight: bold; }}
                .error {{ color: #f44336; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🔍 HubScannerV2 Summary Report</h1>
                <p><strong>Mode:</strong> {mode}</p>
                <p><strong>Run Time:</strong> {data.get('start_time', 'N/A')} - {data.get('end_time', 'N/A')}</p>
                <p><strong>Duration:</strong> {data.get('duration', 'N/A')}</p>
            </div>
            
            <div class="summary">
                <h2>📊 Overall Results</h2>
                <p><strong>Total Jobs Scraped:</strong> <span class="success">{data.get('total_jobs_scraped', 0)}</span></p>
                <p><strong>Sites Processed:</strong> {data.get('sites_processed', 0)}</p>
                <p><strong>Status:</strong> <span class="success">✅ Completed Successfully</span></p>
            </div>
        """
        
        # Add site-specific results
        for site_data in data.get('site_results', []):
            html += f"""
            <div class="site-results">
                <div class="site-header">
                    <h3>{site_data['site_name']} ({site_data['city']})</h3>
                </div>
                <div class="job-list">
                    <p><strong>Pages Scraped:</strong> {site_data.get('pages_scraped', 0)}</p>
                    <p><strong>Jobs Found:</strong> {site_data.get('jobs_found', 0)}</p>
                    <p><strong>Initial Link:</strong> <a href="{site_data.get('url', '#')}">{site_data.get('url', 'N/A')}</a></p>
                </div>
            </div>
            """
        
        # Add cleanup results
        cleanup = data.get('cleanup_results', {})
        html += f"""
            <div class="cleanup">
                <h2>🧹 Duplicate Cleanup</h2>
                <p><strong>Jobs Before Cleanup:</strong> {cleanup.get('before', 0)}</p>
                <p><strong>Jobs After Cleanup:</strong> {cleanup.get('after', 0)}</p>
                <p><strong>Duplicates Removed:</strong> <span class="warning">{cleanup.get('removed', 0)}</span></p>
            </div>
            
            <div class="footer">
                <p>This is an automated report from HubScannerV2</p>
                <p>Generated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def _create_text_summary(self, data):
        """Create plain text email content"""
        mode = "TEST MODE" if data.get('test_mode') else "FULL MODE"
        
        text = f"""
HubScannerV2 Summary Report
==========================

Mode: {mode}
Run Time: {data.get('start_time', 'N/A')} - {data.get('end_time', 'N/A')}
Duration: {data.get('duration', 'N/A')}

Overall Results:
- Total Jobs Scraped: {data.get('total_jobs_scraped', 0)}
- Sites Processed: {data.get('sites_processed', 0)}
- Status: Completed Successfully

Site Results:
"""
        
        for site_data in data.get('site_results', []):
            text += f"""
{site_data['site_name']} ({site_data['city']}):
  - Pages Scraped: {site_data.get('pages_scraped', 0)}
  - Jobs Found: {site_data.get('jobs_found', 0)}
  - Initial Link: {site_data.get('url', 'N/A')}
"""
        
        cleanup = data.get('cleanup_results', {})
        text += f"""
Duplicate Cleanup:
- Jobs Before: {cleanup.get('before', 0)}
- Jobs After: {cleanup.get('after', 0)}
- Duplicates Removed: {cleanup.get('removed', 0)}

---
This is an automated report from HubScannerV2
Generated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        return text.strip()
