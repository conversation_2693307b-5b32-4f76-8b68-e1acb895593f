# HubScannerV2 Heroku Deployment Guide

## 🚀 Quick Deployment

### Option 1: Automated Script (Recommended)

**Windows:**
```bash
deploy_heroku.bat
```

**Linux/Mac:**
```bash
chmod +x deploy_heroku.sh
./deploy_heroku.sh
```

### Option 2: Manual Deployment

## 📋 Pre-Deployment Checklist

- [ ] Heroku CLI installed and logged in
- [ ] Git repository initialized
- [ ] MySQL database accessible from internet
- [ ] API keys ready:
  - [ ] Scrapfly API key
  - [ ] OpenAI API key  
  - [ ] SendGrid API key

## 🔧 Manual Deployment Steps

### 1. Create Heroku App
```bash
heroku create your-app-name
```

### 2. Set Environment Variables
```bash
# Database Configuration
heroku config:set MYSQL_HOST=your-mysql-host
heroku config:set MYSQL_USER=your-mysql-user
heroku config:set MYSQL_PASSWORD=your-mysql-password
heroku config:set MYSQL_DATABASE=hubscannerv2

# API Keys
heroku config:set SCRAPFLY_API_KEY=your-scrapfly-api-key
heroku config:set OPENAI_API_KEY=your-openai-api-key
heroku config:set SENDGRID_API_KEY=your-sendgrid-api-key

# Email Settings (Optional)
heroku config:set EMAIL_SENDER=<EMAIL>
heroku config:set EMAIL_RECIPIENT=<EMAIL>
```

### 3. Add Heroku Scheduler
```bash
heroku addons:create scheduler:standard
```

### 4. Deploy Code
```bash
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

### 5. Configure Scheduled Jobs
```bash
heroku addons:open scheduler
```

Add these jobs in the Heroku Scheduler dashboard:
- **Morning Run**: `python main.py` at `08:00 UTC`
- **Evening Run**: `python main.py` at `20:00 UTC`

## 🧪 Testing Deployment

### Test Configuration
```bash
heroku run python heroku_config.py
```

### Test Single Site Scraping
```bash
heroku run python main.py 1
```

### Monitor Logs
```bash
heroku logs --tail
```

## 📊 Monitoring & Maintenance

### View Configuration
```bash
heroku config
```

### Check Scheduler Jobs
```bash
heroku addons:open scheduler
```

### View Recent Logs
```bash
heroku logs --tail --num 100
```

### Scale Workers (if needed)
```bash
# Scale up for testing
heroku ps:scale worker=1

# Scale down to save costs
heroku ps:scale worker=0
```

## 🔒 Security Best Practices

1. **Environment Variables**: Never commit API keys to git
2. **Database Access**: Ensure MySQL allows connections from Heroku IPs
3. **API Rate Limits**: Monitor API usage to avoid rate limiting
4. **Email Limits**: Check SendGrid sending limits

## 💰 Cost Optimization

### Heroku Costs
- **Basic Dyno**: $7/month (if worker runs continuously)
- **Scheduler**: $25/month
- **Total**: ~$32/month for scheduled runs

### Recommended Setup
- **Worker Dynos**: 0 (use scheduler only)
- **Scheduler Jobs**: 2 per day
- **Cost**: $25/month (scheduler only)

## 🐛 Troubleshooting

### Common Issues

**1. Database Connection Failed**
```bash
# Check database config
heroku config:get MYSQL_HOST
heroku config:get MYSQL_USER

# Test connection
heroku run python -c "from database.db_manager import DatabaseManager; from heroku_config import load_heroku_config; db = DatabaseManager(load_heroku_config()); print('✅ Database connected')"
```

**2. API Key Issues**
```bash
# Check API keys are set
heroku config:get SCRAPFLY_API_KEY
heroku config:get OPENAI_API_KEY
heroku config:get SENDGRID_API_KEY
```

**3. Email Not Sending**
```bash
# Test email configuration
heroku run python -c "from email_sender import EmailSender; from heroku_config import load_heroku_config; sender = EmailSender(load_heroku_config()); print('✅ Email config loaded')"
```

**4. Scheduler Not Running**
- Check scheduler dashboard: `heroku addons:open scheduler`
- Verify job commands are correct
- Check logs during scheduled times

### Debug Commands
```bash
# View all environment variables
heroku config

# Test configuration loading
heroku run python heroku_config.py

# Test database connection
heroku run python -c "from database.db_manager import DatabaseManager; from heroku_config import load_heroku_config; DatabaseManager(load_heroku_config())"

# Test scraper import
heroku run python -c "from main import load_config; print('✅ Main imports successful')"
```

## 📈 Performance Monitoring

### Key Metrics to Monitor
- **Job Success Rate**: Check email reports
- **API Usage**: Monitor Scrapfly/OpenAI usage
- **Database Growth**: Track job table size
- **Email Delivery**: Monitor SendGrid dashboard

### Optimization Tips
1. **Reduce API Calls**: Local patterns are preferred over AI
2. **Database Cleanup**: Regular duplicate removal
3. **Error Handling**: Monitor logs for recurring issues
4. **Rate Limiting**: Respect site rate limits

## 🔄 Updates & Maintenance

### Deploying Updates
```bash
git add .
git commit -m "Update description"
git push heroku main
```

### Database Migrations
```bash
# If schema changes are needed
heroku run python -c "from database.db_manager import DatabaseManager; from heroku_config import load_heroku_config; # Add migration code here"
```

### Backup Strategy
- **Database**: Regular MySQL backups
- **Configuration**: Document all environment variables
- **Code**: Git repository with tags for releases

## ✅ Post-Deployment Checklist

- [ ] Scheduler jobs configured (2 per day)
- [ ] Test email received successfully
- [ ] Database connection working
- [ ] All API keys validated
- [ ] Logs showing successful runs
- [ ] Worker dynos scaled to 0 (cost optimization)
- [ ] Monitoring alerts configured

## 📞 Support

For deployment issues:
1. Check logs: `heroku logs --tail`
2. Test configuration: `heroku run python heroku_config.py`
3. Verify environment variables: `heroku config`
4. Test individual components as shown in troubleshooting section
