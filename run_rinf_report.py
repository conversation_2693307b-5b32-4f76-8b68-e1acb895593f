#!/usr/bin/env python3
"""
CLI runner for RINF Detailed Report Analysis
"""
import asyncio
import argparse
import sys
from datetime import datetime
import yaml
from rinfReport import RINFReportAnalyzer, should_run_today
from heroku_config import load_heroku_config

def load_rinf_report_config():
    """Load RINF Report specific configuration"""
    try:
        with open('config/rinf_report_config.yaml', 'r') as file:
            return yaml.safe_load(file)
    except FileNotFoundError:
        print("⚠️ RINF Report config file not found, using defaults")
        return {}
    except Exception as e:
        print(f"❌ Error loading RINF Report config: {e}")
        return {}

async def run_analysis(args):
    """Run the RINF detailed analysis"""
    try:
        config = load_heroku_config()
        rinf_config = load_rinf_report_config()

        print("🚀 Starting RINF Business Intelligence Analysis...")
        print(f"📅 Analysis period: {args.days} days back")
        print(f"🏢 Minimum positions per company: {args.min_jobs}")
        print(f"🏙️ RINF Cities: {', '.join(rinf_config.get('cities', []))}")
        print(f"📊 Analysis Type: Detailed Business Intelligence")

        print("-" * 60)

        # Create analyzer with rinf_config
        analyzer = RINFReportAnalyzer(config, rinf_config)

        # Run analysis
        await analyzer.run_analysis(days_back=args.days, min_jobs=args.min_jobs, force_run=args.force)

        print("-" * 60)
        print("✅ RINF Business Intelligence analysis completed successfully!")

    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        sys.exit(1)

async def test_connections(args):
    """Test database and email connections"""
    try:
        config = load_heroku_config()
        rinf_config = load_rinf_report_config()

        print("🧪 Testing RINF Report connections...")
        print("-" * 60)

        # Test database connection
        print("📊 Testing database connection...")
        analyzer = RINFReportAnalyzer(config, rinf_config)
        
        # Test basic query
        cursor = analyzer.db_manager.connection.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            print("✅ Database connection successful")
        else:
            print("❌ Database connection failed")
            return

        # Test RINF cities
        print("🏙️ Testing RINF cities...")
        cities = await analyzer.get_rinf_cities()
        print(f"✅ RINF cities configured: {', '.join(cities)}")

        # Test email configuration
        print("📧 Testing email configuration...")
        recipients = rinf_config.get('emails', [])
        if recipients:
            print(f"✅ Email recipients configured: {', '.join(recipients)}")
        else:
            print("⚠️ No email recipients configured")

        # Test OpenAI configuration
        print("🤖 Testing OpenAI configuration...")
        if analyzer.openai_client.api_key:
            print("✅ OpenAI API key configured")
        else:
            print("❌ OpenAI API key not configured")

        analyzer.db_manager.close()
        print("-" * 60)
        print("✅ All connections tested successfully!")

    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        sys.exit(1)

def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(
        description='RINF Business Intelligence Report - Detailed Company Analysis for RINF Customer',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_rinf_report.py run                        # Run with default settings (7 days, 3+ positions)
  python run_rinf_report.py run --force                # Force run even if not Monday
  python run_rinf_report.py run --days 14              # Analyze last 14 days
  python run_rinf_report.py run --min-jobs 5           # Require 5+ positions per company
  python run_rinf_report.py test                       # Test database and email connections
        """
    )

    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Run command
    run_parser = subparsers.add_parser('run', help='Run the RINF detailed business intelligence analysis')
    run_parser.add_argument('--force', action='store_true',
                           help='Force run even if not scheduled day (Mondays)')
    run_parser.add_argument('--days', type=int, default=7,
                           help='Number of days to look back (default: 7)')
    run_parser.add_argument('--min-jobs', type=int, default=3,
                           help='Minimum unique positions per company (default: 3)')
    run_parser.set_defaults(func=run_analysis)

    # Test command
    test_parser = subparsers.add_parser('test', help='Test database and email connections')
    test_parser.set_defaults(func=test_connections)

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        sys.exit(1)

    # Check if should run (unless forced or testing)
    if args.command == 'run' and not args.force and not should_run_today():
        print("RINF Business Intelligence Report is scheduled to run on Mondays.")
        print("Use --force to run anyway, or 'test' to check connections.")
        return

    # Run the selected command
    asyncio.run(args.func(args))

if __name__ == "__main__":
    main()
