@echo off
setlocal enabledelayedexpansion

echo 🚀 HubScannerV2 Heroku Deployment Script
echo ========================================

REM Check if Heroku CLI is installed
heroku --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Heroku CLI is not installed. Please install it first:
    echo    https://devcenter.heroku.com/articles/heroku-cli
    pause
    exit /b 1
)

REM Check if user is logged in to Heroku
heroku auth:whoami >nul 2>&1
if errorlevel 1 (
    echo ❌ Please log in to Heroku first:
    echo    heroku login
    pause
    exit /b 1
)

REM Get app name
set /p APP_NAME="Enter your Heroku app name: "

if "%APP_NAME%"=="" (
    echo ❌ App name cannot be empty
    pause
    exit /b 1
)

echo 📱 Creating Heroku app: %APP_NAME%
heroku create %APP_NAME% 2>nul || echo App might already exist, continuing...

echo 🔧 Setting up environment variables...

REM Database configuration
set /p MYSQL_HOST="MySQL Host: "
set /p MYSQL_USER="MySQL User: "
set /p MYSQL_PASSWORD="MySQL Password: "
set /p MYSQL_DATABASE="MySQL Database: "

REM API Keys
set /p SCRAPFLY_API_KEY="Scrapfly API Key: "
set /p OPENAI_API_KEY="OpenAI API Key: "
set /p SENDGRID_API_KEY="SendGrid API Key: "

REM Email settings
set /p EMAIL_SENDER="Email Sender (default: <EMAIL>): "
if "%EMAIL_SENDER%"=="" set EMAIL_SENDER=<EMAIL>

set /p EMAIL_RECIPIENT="Email Recipient (default: <EMAIL>): "
if "%EMAIL_RECIPIENT%"=="" set EMAIL_RECIPIENT=<EMAIL>

echo 🔑 Setting environment variables on Heroku...

heroku config:set MYSQL_HOST="%MYSQL_HOST%" MYSQL_USER="%MYSQL_USER%" MYSQL_PASSWORD="%MYSQL_PASSWORD%" MYSQL_DATABASE="%MYSQL_DATABASE%" SCRAPFLY_API_KEY="%SCRAPFLY_API_KEY%" OPENAI_API_KEY="%OPENAI_API_KEY%" SENDGRID_API_KEY="%SENDGRID_API_KEY%" EMAIL_SENDER="%EMAIL_SENDER%" EMAIL_RECIPIENT="%EMAIL_RECIPIENT%" --app %APP_NAME%

echo 📅 Adding Heroku Scheduler addon...
heroku addons:create scheduler:standard --app %APP_NAME%

echo 🚀 Deploying to Heroku...
git add .
git commit -m "Deploy HubScannerV2 to Heroku" 2>nul || echo No changes to commit
git push heroku main

echo ✅ Deployment complete!
echo.
echo 📋 Next steps:
echo 1. Configure scheduled jobs:
echo    heroku addons:open scheduler --app %APP_NAME%
echo.
echo 2. Add these scheduled jobs:
echo    - Morning run: 'python main.py' at 08:00 UTC
echo    - Evening run: 'python main.py' at 20:00 UTC
echo.
echo 3. Monitor logs:
echo    heroku logs --tail --app %APP_NAME%
echo.
echo 4. Test the deployment:
echo    heroku run python main.py 1 --app %APP_NAME%
echo.
echo 🎉 HubScannerV2 is now deployed on Heroku!
pause
