from scrapers.base_scraper import Base<PERSON><PERSON>raper
import logging
import re
from urllib.parse import urljoin, urlparse, parse_qs, urlencode, urlunparse
import json

class StepStoneScraper(BaseScraper):
    def __init__(self, config, db_manager, processor):
        super().__init__(config, db_manager, processor)
        self.site_name = "StepStone"
        self.seen_jobs = set()  # Track seen jobs to prevent duplicates
        
        # Country-specific configurations
        self.country_configs = {
            'de': {
                'domain': 'stepstone.de',
                'selectors': {
                    'job_container': ['article[data-testid="job-item"]', 'article.res-4em2ed', 'div[data-testid="job-card-content"]'],
                    'title': ['h2 a', 'a[data-at="job-item-title"]', '[data-testid="job-title"] a', '.job-element-title a'],
                    'company': ['span[data-at="job-item-company-name"]', '[data-testid="company-name"]', '.company-name', '.job-element-company'],
                    'location': ['span[data-at="job-item-location"]', '[data-testid="job-location"]', '.job-location', '.location'],
                    'pagination': ['a[data-testid="pagination-next"]', 'a.pagination-next', 'a[aria-label="Next"]']
                }
            },
            'at': {
                'domain': 'stepstone.at',
                'selectors': {
                    'job_container': ['article[data-testid="job-item"]', 'div.job-element'],
                    'title': ['h2 a', '.job-element-title a'],
                    'company': ['[data-testid="company-name"]', '.company-name'],
                    'location': ['[data-testid="job-location"]', '.location'],
                    'pagination': ['a[data-testid="pagination-next"]', 'a.pagination-next']
                }
            },
            'be': {
                'domain': 'stepstone.be',
                'selectors': {
                    'job_container': ['article[data-testid="job-item"]', 'div.job-element'],
                    'title': ['h2 a', '.job-element-title a'],
                    'company': ['[data-testid="company-name"]', '.company-name'],
                    'location': ['[data-testid="job-location"]', '.location'],
                    'pagination': ['a[data-testid="pagination-next"]', 'a.pagination-next']
                }
            }
        }
        
    def extract_job_listings(self, soup, site_name):
        """Extract job listings using StepStone-specific patterns"""
        
        # Detect country from URL or use default
        country = self._detect_country(soup)
        config = self.country_configs.get(country, self.country_configs['de'])
        
        logging.info(f"Using StepStone {country.upper()} configuration")
        
        # Try StepStone-specific selectors
        job_containers = []
        for selector in config['selectors']['job_container']:
            containers = soup.select(selector)
            if containers:
                job_containers = containers
                logging.info(f"Found {len(containers)} StepStone job containers using selector: {selector}")
                break
                
        if not job_containers:
            logging.warning("No StepStone job containers found with local patterns, falling back to AI")
            return self._fallback_to_ai(soup, site_name)
            
        new_jobs = []
        duplicate_count = 0

        for container in job_containers:
            try:
                job = self._extract_job_from_container(container, soup, config)
                if job and job.get('title') and job.get('url'):
                    # Create job signature for duplicate detection
                    job_signature = f"{job['title']}|{job['company']}"

                    if job_signature not in self.seen_jobs:
                        self.seen_jobs.add(job_signature)
                        new_jobs.append(job)
                        logging.info(f"Added StepStone job: {job['title']} at {job['company']}")
                    else:
                        duplicate_count += 1

            except Exception as e:
                logging.error(f"Error extracting StepStone job: {e}")

        # Log duplicate detection results
        if duplicate_count > 0:
            logging.info(f"StepStone: Found {duplicate_count} duplicate jobs, {len(new_jobs)} new jobs")

        logging.info(f"Extracted {len(new_jobs)} jobs from StepStone using local patterns")
        return new_jobs
    
    def _detect_country(self, soup):
        """Detect StepStone country from page content"""
        # Try to detect from canonical URL
        canonical = soup.find('link', {'rel': 'canonical'})
        if canonical and canonical.get('href'):
            url = canonical.get('href')
            if 'stepstone.de' in url:
                return 'de'
            elif 'stepstone.at' in url:
                return 'at'
            elif 'stepstone.be' in url:
                return 'be'
                
        # Try to detect from page language
        html_tag = soup.find('html')
        if html_tag and html_tag.get('lang'):
            lang = html_tag.get('lang').lower()
            if lang.startswith('de'):
                return 'de'
            elif lang.startswith('nl'):
                return 'be'
                
        # Default to German
        return 'de'
    
    def _extract_job_from_container(self, container, _soup, config):
        """Extract job data from StepStone container using local patterns"""
        job = {}
        
        # Extract title
        title_elem = None
        for selector in config['selectors']['title']:
            title_elem = container.select_one(selector)
            if title_elem:
                break
                
        job['title'] = title_elem.get_text(strip=True) if title_elem else ""
        
        # Extract company
        company_elem = None
        for selector in config['selectors']['company']:
            company_elem = container.select_one(selector)
            if company_elem:
                break
                
        job['company'] = company_elem.get_text(strip=True) if company_elem else ""
        
        # Extract location
        location_elem = None
        for selector in config['selectors']['location']:
            location_elem = container.select_one(selector)
            if location_elem:
                break
                
        job['location'] = location_elem.get_text(strip=True) if location_elem else ""
        
        # Extract URL
        url_elem = None
        for selector in config['selectors']['title']:
            url_elem = container.select_one(selector)
            if url_elem and url_elem.get('href'):
                break
                
        if url_elem and url_elem.get('href'):
            base_url = f"https://www.{config['domain']}"
            job['url'] = urljoin(base_url, url_elem.get('href'))
        else:
            job['url'] = ""
            
        # Extract description/summary
        desc_elem = (container.select_one('.job-element-summary') or 
                    container.select_one('.job-summary') or
                    container.select_one('[data-testid="job-summary"]'))
        job['description'] = desc_elem.get_text(strip=True) if desc_elem else ""
        
        # Extract salary if available
        salary_elem = container.select_one('.salary') or container.select_one('[data-testid="salary"]')
        job['salary'] = salary_elem.get_text(strip=True) if salary_elem else ""
        
        # Set source site
        job['source_site'] = 'StepStone'
        
        # Extract city from location
        job['city'] = self._extract_city_from_location(job['location'])
        
        return job
    
    def extract_next_page(self, soup, current_url):
        """Extract next page URL using StepStone-specific patterns"""
        # Try local patterns first
        next_page_url = self._extract_next_page_local(soup, current_url)
        
        if next_page_url:
            logging.info(f"Found next page using local patterns: {next_page_url}")
            return next_page_url
            
        # Fallback to AI if local patterns fail
        logging.info("Local pagination patterns failed, falling back to AI")
        return self._fallback_pagination_to_ai(soup, current_url)
    
    def _extract_next_page_local(self, soup, current_url):
        """Extract next page using StepStone-specific local patterns"""
        # Detect country configuration
        country = self._detect_country(soup)
        config = self.country_configs.get(country, self.country_configs['de'])
        
        # Method 1: Look for next page link using country-specific selectors
        for selector in config['selectors']['pagination']:
            next_link = soup.select_one(selector)
            if next_link and next_link.get('href'):
                return urljoin(current_url, next_link.get('href'))
                
        # Method 2: Look for pagination with page numbers
        pagination_links = soup.select('.pagination a, [data-testid="pagination"] a')
        
        for link in pagination_links:
            link_text = link.get_text(strip=True).lower()
            if any(word in link_text for word in ['next', 'weiter', 'volgende', 'suivant']):
                return urljoin(current_url, link.get('href'))
                
        # Method 3: Construct next page URL from current URL
        parsed_url = urlparse(current_url)
        query_params = parse_qs(parsed_url.query)
        
        # StepStone uses 'page' parameter
        current_page = int(query_params.get('page', ['1'])[0])
        next_page = current_page + 1
        
        # Update page parameter
        query_params['page'] = [str(next_page)]
        
        # Reconstruct URL
        new_query = urlencode(query_params, doseq=True)
        next_url = urlunparse((
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            new_query,
            parsed_url.fragment
        ))
        
        # Only return if we actually incremented the page
        if next_page > current_page:
            return next_url
            
        return None
    
    def _extract_city_from_location(self, location):
        """Extract city from location string"""
        if not location:
            return ""
            
        # Common patterns: "City, State", "City (State)", "Remote in City"
        location_parts = location.split(',')
        if len(location_parts) > 0:
            city = location_parts[0].strip()
            # Remove "Remote" or similar prefixes in multiple languages
            city = re.sub(r'^(Remote|Homeoffice|Home Office|Fernarbeit|Télétravail|Thuiswerk)\s*(in|à|in)?\s*', '', city, flags=re.IGNORECASE)
            return city.strip()
            
        return ""

    def reset_seen_jobs(self):
        """Reset seen jobs tracker (call this when starting a new scraping session)"""
        self.seen_jobs.clear()

    def _fallback_to_ai(self, soup, site_name):
        """Fallback to AI extraction when local patterns fail"""
        from scrapers.ai_scraper import AIScraper
        ai_scraper = AIScraper(self.config, self.db_manager, self.processor)
        return ai_scraper.extract_job_listings(soup, site_name)
    
    def _fallback_pagination_to_ai(self, soup, current_url):
        """Fallback to AI pagination when local patterns fail"""
        from scrapers.ai_scraper import AIScraper
        ai_scraper = AIScraper(self.config, self.db_manager, self.processor)
        return ai_scraper.extract_next_page(soup, current_url)
