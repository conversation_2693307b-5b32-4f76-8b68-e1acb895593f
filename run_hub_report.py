#!/usr/bin/env python3
"""
HubReport CLI - Command Line Interface for running business intelligence reports
"""
import argparse
import asyncio
import sys
from datetime import datetime
import yaml

from hubReport import HubReportAnalyzer, should_run_today
from heroku_config import load_heroku_config

def load_hub_report_config():
    """Load HubReport specific configuration"""
    try:
        with open('config/hub_report_config.yaml', 'r') as file:
            return yaml.safe_load(file)
    except FileNotFoundError:
        print("⚠️ HubReport config file not found, using defaults")
        return {}
    except Exception as e:
        print(f"❌ Error loading HubReport config: {e}")
        return {}

async def run_analysis(args):
    """Run the HubReport analysis"""
    try:
        # Load configurations
        config = load_heroku_config()
        hub_config = load_hub_report_config()
        
        # Override with command line arguments
        days_back = args.days or hub_config.get('analysis', {}).get('days_back', 7)
        min_jobs = args.min_jobs or hub_config.get('analysis', {}).get('min_unique_positions', 3)
        
        print(f"Starting HubReport Analysis")
        print(f"Looking back {days_back} days")
        print(f"Minimum {min_jobs} unique positions per company")
        print(f"Started at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("-" * 50)
        
        # Create and run analyzer
        analyzer = HubReportAnalyzer(config, hub_config)
        await analyzer.run_analysis(days_back=days_back, min_jobs=min_jobs)
        
        print("-" * 50)
        print("HubReport analysis completed successfully!")

    except Exception as e:
        print(f"Analysis failed: {e}")
        sys.exit(1)

async def test_connection(_args):
    """Test database and email connections"""
    try:
        config = load_heroku_config()
        
        print("Testing connections...")

        # Test database
        print("Testing database connection...")
        analyzer = HubReportAnalyzer(config)

        # Simple query to test connection
        cursor = analyzer.db_manager.connection.cursor()
        cursor.execute("SELECT COUNT(*) as job_count FROM jobs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")
        result = cursor.fetchone()
        cursor.close()

        print(f"Database connected - Found {result[0]} jobs in last 7 days")

        # Test email configuration
        print("Testing email configuration...")
        email_config = config.get('email', {})
        if email_config.get('sendgrid_api_key'):
            print("SendGrid API key configured")
        else:
            print("SendGrid API key missing")

        if email_config.get('recipient'):
            print(f"Email recipient: {email_config['recipient']}")
        else:
            print("Email recipient not configured")

        analyzer.db_manager.close()
        print("All connections tested successfully!")

    except Exception as e:
        print(f"Connection test failed: {e}")
        sys.exit(1)

async def show_stats(_args):
    """Show statistics about available data"""
    try:
        config = load_heroku_config()
        analyzer = HubReportAnalyzer(config)
        
        print("HubReport Data Statistics")
        print("=" * 40)

        # Get basic stats
        cursor = analyzer.db_manager.connection.cursor(dictionary=True)

        # Total jobs
        cursor.execute("SELECT COUNT(*) as total FROM jobs")
        total_jobs = cursor.fetchone()['total']

        # Jobs in last 7 days
        cursor.execute("SELECT COUNT(*) as recent FROM jobs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")
        recent_jobs = cursor.fetchone()['recent']

        # Unique companies in last 7 days
        cursor.execute("""
            SELECT COUNT(DISTINCT company) as companies
            FROM jobs
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            AND company IS NOT NULL AND company != ''
        """)
        recent_companies = cursor.fetchone()['companies']

        # Companies with multiple positions
        cursor.execute("""
            SELECT COUNT(*) as multi_position_companies
            FROM (
                SELECT company, COUNT(DISTINCT title) as positions
                FROM jobs
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                AND company IS NOT NULL AND company != ''
                GROUP BY company
                HAVING positions >= 3
            ) as subquery
        """)
        multi_position = cursor.fetchone()['multi_position_companies']

        # Top cities
        cursor.execute("""
            SELECT city, COUNT(*) as job_count
            FROM jobs
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            AND city IS NOT NULL AND city != ''
            GROUP BY city
            ORDER BY job_count DESC
            LIMIT 5
        """)
        top_cities = cursor.fetchall()

        cursor.close()
        analyzer.db_manager.close()

        print(f"Total Jobs in Database: {total_jobs:,}")
        print(f"Jobs in Last 7 Days: {recent_jobs:,}")
        print(f"Unique Companies (7 days): {recent_companies:,}")
        print(f"Companies with 3+ Positions: {multi_position:,}")
        print()
        print("Top Cities (Last 7 Days):")
        for city in top_cities:
            print(f"   - {city['city']}: {city['job_count']} jobs")

        print()
        print(f"Analysis Potential: {multi_position} companies qualify for outsourcing analysis")

    except Exception as e:
        print(f"Failed to get statistics: {e}")
        sys.exit(1)

def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(
        description="HubReport - Business Intelligence for IT Outsourcing Opportunities",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_hub_report.py run --force                    # Force run analysis
  python run_hub_report.py run --days 14 --min-jobs 5    # Custom parameters
  python run_hub_report.py test                           # Test connections
  python run_hub_report.py stats                          # Show data statistics
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Run analysis command
    run_parser = subparsers.add_parser('run', help='Run HubReport analysis')
    run_parser.add_argument('--force', action='store_true', 
                           help='Force run even if not scheduled day')
    run_parser.add_argument('--days', type=int, 
                           help='Number of days to look back (default: 7)')
    run_parser.add_argument('--min-jobs', type=int, 
                           help='Minimum unique positions per company (default: 3)')
    
    # Test connections command
    test_parser = subparsers.add_parser('test', help='Test database and email connections')
    
    # Show statistics command
    stats_parser = subparsers.add_parser('stats', help='Show data statistics')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Check if should run (unless forced or testing)
    if args.command == 'run' and not args.force and not should_run_today():
        print("HubReport is scheduled to run on Mondays.")
        print("Use --force to run anyway, or 'test' to check connections.")
        return
    
    # Execute command
    if args.command == 'run':
        asyncio.run(run_analysis(args))
    elif args.command == 'test':
        asyncio.run(test_connection(args))
    elif args.command == 'stats':
        asyncio.run(show_stats(args))

if __name__ == "__main__":
    main()
