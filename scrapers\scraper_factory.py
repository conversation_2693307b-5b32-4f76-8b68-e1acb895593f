"""
Scraper Factory - Creates appropriate scraper instances based on site name
"""

import logging
from scrapers.indeed_scraper import IndeedScraper
from scrapers.stepstone_scraper import StepStoneScraper
from scrapers.occ_scraper import OCCScraper
from scrapers.glassdoor_scraper import GlassdoorScraper
from scrapers.ai_scraper import AIScraper

class ScraperFactory:
    """Factory class to create appropriate scrapers based on site name"""
    
    @staticmethod
    def create_scraper(site_name, config, db_manager, processor):
        """
        Create and return the appropriate scraper instance based on site name
        
        Args:
            site_name (str): Name of the job site
            config (dict): Configuration dictionary
            db_manager: Database manager instance
            processor: Job processor instance
            
        Returns:
            BaseScraper: Appropriate scraper instance
        """
        site_name_lower = site_name.lower()
        
        if 'indeed' in site_name_lower:
            logging.info(f"Creating IndeedScraper for {site_name}")
            return IndeedScraper(config, db_manager, processor)
            
        elif 'stepstone' in site_name_lower:
            logging.info(f"Creating StepStoneScraper for {site_name}")
            return StepStoneScraper(config, db_manager, processor)
            
        elif 'occ' in site_name_lower:
            logging.info(f"Creating OCCScraper for {site_name}")
            return OCCScraper(config, db_manager, processor)
            
        elif 'glassdoor' in site_name_lower:
            logging.info(f"Creating GlassdoorScraper for {site_name}")
            return GlassdoorScraper(config, db_manager, processor)
            
        else:
            # Fallback to AI scraper for unknown sites
            logging.info(f"Creating AIScraper for unknown site: {site_name}")
            return AIScraper(config, db_manager, processor)
    
    @staticmethod
    def get_supported_sites():
        """
        Get list of supported sites with dedicated scrapers
        
        Returns:
            list: List of supported site names
        """
        return ['Indeed', 'StepStone', 'OCC', 'Glassdoor']
    
    @staticmethod
    def is_site_supported(site_name):
        """
        Check if a site has a dedicated scraper
        
        Args:
            site_name (str): Name of the job site
            
        Returns:
            bool: True if site has dedicated scraper, False otherwise
        """
        site_name_lower = site_name.lower()
        supported_sites = ['indeed', 'stepstone', 'occ', 'glassdoor']
        
        return any(supported in site_name_lower for supported in supported_sites)
