import logging
import json
from openai import OpenAI

class JobProcessor:
    def __init__(self, config):
        self.config = config
        self.openai_client = OpenAI(api_key=config['openai']['api_key'])

    def process_job(self, job_data):
        """Process and enrich job data using OpenAI"""
        try:
            # Extract skills, experience level, job type, and remote status
            enriched_data = self._extract_job_details(job_data)

            # Update job data with enriched information
            job_data.update(enriched_data)

            return job_data

        except Exception as e:
            logging.error(f"Error processing job: {e}")
            return job_data

    def process_job_locally(self, job_data):
        """Process job data locally without API calls using pattern matching"""
        try:
            # Start with the original job data
            processed_job = job_data.copy()

            # Ensure all required fields exist
            if 'skills' not in processed_job or not processed_job['skills']:
                processed_job['skills'] = self._extract_skills_locally(job_data)

            if 'experience_level' not in processed_job or not processed_job['experience_level']:
                processed_job['experience_level'] = self._extract_experience_level_locally(job_data)

            if 'job_type' not in processed_job or not processed_job['job_type']:
                processed_job['job_type'] = self._extract_job_type_locally(job_data)

            if 'remote_status' not in processed_job or not processed_job['remote_status']:
                processed_job['remote_status'] = self._extract_remote_status_locally(job_data)

            return processed_job

        except Exception as e:
            logging.error(f"Error processing job locally: {e}")
            return job_data

    def _extract_skills_locally(self, job_data):
        """Extract skills from job title and description using pattern matching"""
        text = f"{job_data.get('title', '')} {job_data.get('description', '')}".lower()

        # Common programming languages and technologies
        skills_patterns = [
            'python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust',
            'react', 'angular', 'vue', 'node.js', 'django', 'flask', 'spring', 'laravel',
            'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'git', 'linux',
            'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch',
            'html', 'css', 'sass', 'bootstrap', 'tailwind',
            'machine learning', 'ai', 'tensorflow', 'pytorch', 'pandas', 'numpy',
            'devops', 'ci/cd', 'terraform', 'ansible'
        ]

        found_skills = []
        for skill in skills_patterns:
            if skill in text:
                found_skills.append(skill.title())

        return json.dumps(found_skills) if found_skills else '[]'

    def _extract_experience_level_locally(self, job_data):
        """Extract experience level from job title and description"""
        text = f"{job_data.get('title', '')} {job_data.get('description', '')}".lower()

        if any(word in text for word in ['senior', 'sr.', 'lead', 'principal', 'architect']):
            return 'senior'
        elif any(word in text for word in ['junior', 'jr.', 'entry', 'graduate', 'trainee']):
            return 'junior'
        elif any(word in text for word in ['mid', 'intermediate', '2-5 years', '3-5 years']):
            return 'mid'

        return None

    def _extract_job_type_locally(self, job_data):
        """Extract job type from job title and description"""
        text = f"{job_data.get('title', '')} {job_data.get('description', '')}".lower()

        if any(word in text for word in ['full-time', 'fulltime', 'full time', 'permanent']):
            return 'full-time'
        elif any(word in text for word in ['part-time', 'parttime', 'part time']):
            return 'part-time'
        elif any(word in text for word in ['contract', 'contractor', 'freelance', 'temporary']):
            return 'contract'
        elif any(word in text for word in ['intern', 'internship']):
            return 'internship'

        return 'full-time'  # Default assumption

    def _extract_remote_status_locally(self, job_data):
        """Extract remote status from location and description"""
        text = f"{job_data.get('location', '')} {job_data.get('description', '')}".lower()

        if any(word in text for word in ['remote', 'home office', 'work from home', 'distributed']):
            if any(word in text for word in ['hybrid', 'flexible', 'part remote']):
                return 'hybrid'
            else:
                return 'remote'
        elif any(word in text for word in ['on-site', 'onsite', 'office', 'in-person']):
            return 'on-site'

        return None

    def _extract_job_details(self, job_data):
        """Use OpenAI to extract detailed information from job description"""
        try:
            # Create a prompt for OpenAI
            system_prompt = """
            You are an expert job analyst. Extract the following information from the job details:
            1. Skills required (as a JSON array of strings)
            2. Experience level (entry, mid, senior, executive)
            3. Job type (full-time, part-time, contract, internship)
            4. Remote status (remote, hybrid, on-site)

            Return the data as a JSON object with these keys: skills, experience_level, job_type, remote_status.
            If you cannot determine a value, use null.
            """

            # Combine title and description for better context
            job_context = f"Job Title: {job_data['title']}\nCompany: {job_data['company']}\nLocation: {job_data['location']}\nDescription: {job_data['description']}"

            # Call OpenAI API
            response = self.openai_client.chat.completions.create(
                model="o3-mini",  # Use o3-mini model
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": job_context}
                ],
                response_format={"type": "json_object"}
            )

            # Parse the response
            result = json.loads(response.choices[0].message.content)

            # Ensure skills is a JSON string for database storage
            if 'skills' in result and result['skills']:
                result['skills'] = json.dumps(result['skills'])
            else:
                result['skills'] = json.dumps([])

            return result

        except Exception as e:
            logging.error(f"Error extracting job details: {e}")
            return {
                'skills': json.dumps([]),
                'experience_level': None,
                'job_type': None,
                'remote_status': None
            }

