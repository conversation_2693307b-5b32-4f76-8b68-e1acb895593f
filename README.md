# HubScannerV2

A production-ready job scraping application that extracts software engineering jobs from multiple job sites using AI-powered data extraction.

## Features

- **Multi-site scraping**: Indeed, StepStone, and Glassdoor
- **AI-powered extraction**: Uses OpenAI GPT to extract structured job data
- **Enhanced job data**: Extracts skills, job types, experience levels, and remote status
- **Clean database storage**: MySQL database with optimized schema
- **Single-run execution**: Runs once and exits (perfect for scheduling)
- **Robust error handling**: Comprehensive logging and error recovery

## Architecture

```
hubscannerv2/
├── main.py                 # Application entry point
├── config/
│   └── config.yaml        # Configuration settings
├── database/
│   ├── db_manager.py      # Database operations
│   └── schema.sql         # Database schema
├── processors/
│   └── job_processor.py   # Job data processing
├── scrapers/
│   ├── __init__.py
│   ├── ai_scraper.py      # AI-powered scraper
│   └── base_scraper.py    # Base scraper class
└── requirements.txt       # Python dependencies
```

## Setup

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Database Setup
```bash
# Create MySQL database and run schema
mysql -u your_user -p your_database < database/schema.sql
```

### 3. Environment Variables
Create a `.env` file with:
```
MYSQL_PASSWORD=your_mysql_password
SCRAPFLY_API_KEY=your_scrapfly_key
OPENAI_API_KEY=your_openai_key
```

### 4. Configuration
Update `config/config.yaml` with your database and API settings.

## Usage

### Run Single Scraping Session
```bash
python main.py
```

The application will:
1. Connect to the database
2. Scrape all configured job sites
3. Extract and process job data
4. Save results to database
5. Exit cleanly

### Scheduling
Use external schedulers for automated runs:

**Windows Task Scheduler:**
```
Program: python
Arguments: main.py
Start in: C:\path\to\hubscannerv2
```

**Linux Cron:**
```bash
0 1 * * * cd /path/to/hubscannerv2 && python main.py
```

## Database Schema

### Jobs Table
- `id` - Primary key
- `title` - Job title
- `company` - Company name
- `location` - Job location (extracted)
- `city` - City from scraping link
- `description` - Job description
- `url` - Job URL
- `source_site` - Source job site
- `skills` - JSON array of skills
- `job_type` - Employment type
- `experience_level` - Required experience
- `remote_status` - Remote work options
- `created_at` / `updated_at` - Timestamps

### Scraping Links Table
- `id` - Primary key
- `site_name` - Job site name
- `url` - Scraping URL
- `city` - Target city

## Job Sites Supported

1. **Indeed** (Berlin) - Software Engineer jobs
2. **StepStone** (Munich) - Software Engineer jobs  
3. **Glassdoor** (Munich) - Software Engineer jobs

## Data Quality

The application extracts high-quality structured data:
- **Skills**: 75%+ fill rate with relevant technologies
- **Job Type**: 100% fill rate (full-time, part-time, etc.)
- **Experience Level**: 35%+ fill rate (junior, senior, etc.)
- **Remote Status**: 25%+ fill rate (remote, hybrid, on-site)

## Logging

Comprehensive logging to console with:
- Scraping progress
- Job extraction counts
- Error handling
- Database operations

## Production Ready

- ✅ Single-run execution (no continuous loops)
- ✅ Clean exit after completion
- ✅ Proper error handling and logging
- ✅ Optimized database schema
- ✅ Configurable via YAML and environment variables
- ✅ Ready for external scheduling

## Requirements

- Python 3.8+
- MySQL 5.7+
- OpenAI API key
- Scrapfly API key

## License

Private project - All rights reserved.
