from scrapers.base_scraper import <PERSON><PERSON><PERSON>raper
import logging
import re
from urllib.parse import urljoin, urlparse, parse_qs, urlencode, urlunparse
import json

class OCCScraper(BaseScraper):
    def __init__(self, config, db_manager, processor):
        super().__init__(config, db_manager, processor)
        self.site_name = "OCC"
        
    def extract_job_listings(self, soup, site_name):
        """Extract job listings using OCC-specific patterns"""
        job_listings = []
        
        # OCC-specific selectors (jobcard containers)
        job_containers = soup.select('div[id^="jobcard-"]')
        
        if not job_containers:
            logging.warning("No OCC job containers found with local patterns, falling back to AI")
            return self._fallback_to_ai(soup, site_name)
            
        logging.info(f"Found {len(job_containers)} OCC job containers using local patterns")
        
        for container in job_containers:
            try:
                job = self._extract_job_from_container(container, soup)
                if job and job.get('title') and job.get('url'):
                    job_listings.append(job)
            except Exception as e:
                logging.error(f"Error extracting OCC job: {e}")
                
        logging.info(f"Extracted {len(job_listings)} jobs from OCC using local patterns")
        return job_listings
    
    def _extract_job_from_container(self, container, soup):
        """Extract job data from OCC container using local patterns"""
        job = {}
        
        # Extract jobcard ID from container ID
        container_id = container.get('id', '')
        jobcard_match = re.search(r'jobcard-(\d+)', container_id)
        jobcard_id = jobcard_match.group(1) if jobcard_match else None
        
        # Extract title (usually in h2 elements)
        title_elem = (container.select_one('h2 a') or 
                     container.select_one('h2') or
                     container.select_one('.job-title a') or
                     container.select_one('.job-title'))
        job['title'] = title_elem.get_text(strip=True) if title_elem else ""
        
        # Extract company (usually in span elements, often truncated)
        company_elem = None
        # Look for company in various span elements
        spans = container.select('span')

        # Categorize spans to find the best company candidate
        company_candidates = []

        for span in spans:
            text = span.get_text(strip=True)

            # Skip empty spans
            if not text or len(text) <= 2:
                continue

            # Skip obvious non-company content
            if (re.match(r'^[\d\s,.-]+$', text) or  # Just numbers/punctuation
                re.search(r'\b(hace|ago|días|days|horas|hours)\b', text, re.IGNORECASE) or  # Time indicators
                re.search(r'\b(guadalajara|jalisco|méxico|mexico|cdmx|monterrey|puebla)\b', text, re.IGNORECASE) or  # Location indicators
                re.search(r'sueldo no mostrado', text, re.IGNORECASE) or  # "Salary not shown" text
                re.search(r'^\$[\d,]+\s*-\s*\$[\d,]+', text) or  # Salary ranges like "$15,000 - $20,000"
                re.search(r'^\$[\d,]+\s+(mensual|anual|semanal)', text, re.IGNORECASE) or  # Salary with period like "$15,000 Mensual"
                re.search(r'\b(años|experiencia|experience)\b', text, re.IGNORECASE)):  # Experience indicators
                continue

            # Add to candidates with a priority score
            priority = 0

            # Higher priority for text that looks like company names
            if re.search(r'\b(inc|ltd|llc|corp|corporation|company|empresa|sa|srl|gmbh)\b', text, re.IGNORECASE):
                priority += 10

            # Lower priority for text containing salary indicators (but not excluding completely)
            if re.search(r'\$', text):
                priority -= 5

            # Higher priority for longer, more descriptive text
            if len(text) > 10:
                priority += 2

            company_candidates.append((priority, span, text))

        # Sort by priority and pick the best candidate
        if company_candidates:
            company_candidates.sort(key=lambda x: x[0], reverse=True)
            company_elem = company_candidates[0][1]

        job['company'] = company_elem.get_text(strip=True) if company_elem else ""
        
        # Extract location (look for city/state patterns in spans)
        location_elem = None
        for span in spans:
            text = span.get_text(strip=True)
            # Look for location patterns
            if re.search(r'\b(guadalajara|jalisco|méxico|mexico|cdmx|monterrey|puebla)\b', text, re.IGNORECASE):
                location_elem = span
                break
                
        job['location'] = location_elem.get_text(strip=True) if location_elem else ""
        
        # Generate URL from jobcard ID
        if jobcard_id:
            job['url'] = f"https://www.occ.com.mx/empleo/{jobcard_id}"
        else:
            # Try to find a link in the container
            link_elem = container.select_one('a')
            if link_elem and link_elem.get('href'):
                job['url'] = urljoin('https://www.occ.com.mx', link_elem.get('href'))
            else:
                job['url'] = ""
        
        # Extract description/summary
        desc_elem = (container.select_one('.job-description') or 
                    container.select_one('.summary') or
                    container.select_one('p'))
        job['description'] = desc_elem.get_text(strip=True) if desc_elem else ""
        
        # Extract salary (look for spans containing "$" or "Sueldo")
        salary_elem = None
        for span in spans:
            text = span.get_text(strip=True)
            if '$' in text or 'sueldo' in text.lower():
                salary_elem = span
                break
                
        job['salary'] = salary_elem.get_text(strip=True) if salary_elem else ""
        
        # Extract experience (look for "años" or "experiencia")
        experience_elem = None
        for span in spans:
            text = span.get_text(strip=True)
            if re.search(r'\b(años|experiencia|experience)\b', text, re.IGNORECASE):
                experience_elem = span
                break
                
        job['experience'] = experience_elem.get_text(strip=True) if experience_elem else ""
        
        # Set source site
        job['source_site'] = 'OCC'
        
        # Extract city from location
        job['city'] = self._extract_city_from_location(job['location'])
        
        return job
    
    def extract_next_page(self, soup, current_url):
        """Extract next page URL using OCC-specific patterns"""
        # Try local patterns first
        next_page_url = self._extract_next_page_local(soup, current_url)
        
        if next_page_url:
            logging.info(f"Found next page using local patterns: {next_page_url}")
            return next_page_url
            
        # Fallback to AI if local patterns fail
        logging.info("Local pagination patterns failed, falling back to AI")
        return self._fallback_pagination_to_ai(soup, current_url)
    
    def _extract_next_page_local(self, soup, current_url):
        """Extract next page using OCC-specific local patterns"""
        # Method 1: Look for "Siguiente" or "Next" links
        next_link = (soup.select_one('a[aria-label="Siguiente"]') or 
                    soup.select_one('a[aria-label="Next"]') or
                    soup.select_one('a:contains("Siguiente")') or
                    soup.select_one('a:contains("›")'))
                    
        if next_link and next_link.get('href'):
            return urljoin(current_url, next_link.get('href'))
            
        # Method 2: Look for pagination with page numbers
        pagination_links = soup.select('.pagination a, nav a')
        
        for link in pagination_links:
            link_text = link.get_text(strip=True).lower()
            if any(word in link_text for word in ['siguiente', 'next', '›', '>']):
                return urljoin(current_url, link.get('href'))
                
        # Method 3: Construct next page URL from current URL
        parsed_url = urlparse(current_url)
        query_params = parse_qs(parsed_url.query)
        
        # Check if URL already has page parameter
        if 'page' in query_params:
            current_page = int(query_params['page'][0])
            next_page = current_page + 1
        else:
            # First pagination - add page=2
            next_page = 2
            
        # Update page parameter
        query_params['page'] = [str(next_page)]
        
        # Reconstruct URL
        new_query = urlencode(query_params, doseq=True)
        next_url = urlunparse((
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            new_query,
            parsed_url.fragment
        ))
        
        return next_url
    
    def _extract_city_from_location(self, location):
        """Extract city from location string"""
        if not location:
            return ""
            
        # OCC-specific location patterns: "Guadalajara, Jal.", "Ciudad de México, CDMX"
        location_parts = location.split(',')
        if len(location_parts) > 0:
            city = location_parts[0].strip()
            # Remove common prefixes
            city = re.sub(r'^(Remoto en|Remote in|Home Office en)\s*', '', city, flags=re.IGNORECASE)
            return city.strip()
            
        return ""
    
    def _fallback_to_ai(self, soup, site_name):
        """Fallback to AI extraction when local patterns fail"""
        from scrapers.ai_scraper import AIScraper
        ai_scraper = AIScraper(self.config, self.db_manager, self.processor)
        return ai_scraper.extract_job_listings(soup, site_name)
    
    def _fallback_pagination_to_ai(self, soup, current_url):
        """Fallback to AI pagination when local patterns fail"""
        from scrapers.ai_scraper import AIScraper
        ai_scraper = AIScraper(self.config, self.db_manager, self.processor)
        return ai_scraper.extract_next_page(soup, current_url)
