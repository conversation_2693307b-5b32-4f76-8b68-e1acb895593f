#!/usr/bin/env python3
"""
Heroku configuration loader for HubScannerV2
Loads configuration from environment variables for Heroku deployment
"""
import os
import yaml
from dotenv import load_dotenv

def load_heroku_config():
    """Load configuration for Heroku deployment using environment variables"""
    load_dotenv()
    
    # Check if we're running on Heroku
    is_heroku = os.getenv('DYNO') is not None
    
    if is_heroku:
        # Running on Heroku - use environment variables
        config = {
            'mysql': {
                'host': os.getenv('MYSQL_HOST'),
                'user': os.getenv('MYSQL_USER'),
                'password': os.getenv('MYSQL_PASSWORD'),
                'database': os.getenv('MYSQL_DATABASE')
            },
            'scrapfly': {
                'api_key': os.getenv('SCRAPFLY_API_KEY')
            },
            'openai': {
                'api_key': os.getenv('OPENAI_API_KEY')
            },
            'email': {
                'sendgrid_api_key': os.getenv('SENDGRID_API_KEY'),
                'smtp_host': 'smtp.sendgrid.net',
                'smtp_port': 587,
                'sender': os.getenv('EMAIL_SENDER', '<EMAIL>'),
                'recipient': os.getenv('EMAIL_RECIPIENT', '<EMAIL>')
            },
            'scraping': {
                'delay_seconds': int(os.getenv('SCRAPING_DELAY', '5')),
                'max_retries': int(os.getenv('SCRAPING_MAX_RETRIES', '3')),
                'timeout_seconds': int(os.getenv('SCRAPING_TIMEOUT', '30'))
            }
        }
        
        # Validate required environment variables
        required_vars = [
            'MYSQL_HOST', 'MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DATABASE',
            'SCRAPFLY_API_KEY', 'OPENAI_API_KEY', 'SENDGRID_API_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        print("✅ Loaded configuration from Heroku environment variables")
        return config
    
    else:
        # Running locally - use config.yaml
        try:
            with open('config/config.yaml', 'r') as file:
                config = yaml.safe_load(file)
            
            # Override with environment variables if they exist
            if os.getenv('MYSQL_HOST'):
                config['mysql']['host'] = os.getenv('MYSQL_HOST')
            if os.getenv('MYSQL_USER'):
                config['mysql']['user'] = os.getenv('MYSQL_USER')
            if os.getenv('MYSQL_PASSWORD'):
                config['mysql']['password'] = os.getenv('MYSQL_PASSWORD')
            if os.getenv('MYSQL_DATABASE'):
                config['mysql']['database'] = os.getenv('MYSQL_DATABASE')
            if os.getenv('SCRAPFLY_API_KEY'):
                config['scrapfly']['api_key'] = os.getenv('SCRAPFLY_API_KEY')
            if os.getenv('OPENAI_API_KEY'):
                config['openai']['api_key'] = os.getenv('OPENAI_API_KEY')
            if os.getenv('SENDGRID_API_KEY'):
                config['email']['sendgrid_api_key'] = os.getenv('SENDGRID_API_KEY')
            if os.getenv('EMAIL_RECIPIENT'):
                config['email']['recipient'] = os.getenv('EMAIL_RECIPIENT')
            if os.getenv('EMAIL_SENDER'):
                config['email']['sender'] = os.getenv('EMAIL_SENDER')
            
            print("✅ Loaded configuration from config.yaml with environment overrides")
            return config
            
        except Exception as e:
            raise ValueError(f"Failed to load local configuration: {e}")

def get_database_url():
    """Get database URL for Heroku (if DATABASE_URL is provided)"""
    database_url = os.getenv('DATABASE_URL')
    if database_url:
        # Parse DATABASE_URL format: mysql://user:password@host:port/database
        import re
        match = re.match(r'mysql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', database_url)
        if match:
            user, password, host, port, database = match.groups()
            return {
                'host': host,
                'port': int(port),
                'user': user,
                'password': password,
                'database': database
            }
    return None

if __name__ == "__main__":
    try:
        config = load_heroku_config()
        print("✅ Configuration loaded successfully!")
        print(f"📊 Configuration summary:")
        print(f"   - MySQL Host: {config['mysql']['host']}")
        print(f"   - Email Recipient: {config['email']['recipient']}")
        print(f"   - Scrapfly API Key: {'✅ Set' if config['scrapfly']['api_key'] else '❌ Missing'}")
        print(f"   - OpenAI API Key: {'✅ Set' if config['openai']['api_key'] else '❌ Missing'}")
        print(f"   - SendGrid API Key: {'✅ Set' if config['email']['sendgrid_api_key'] else '❌ Missing'}")
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
