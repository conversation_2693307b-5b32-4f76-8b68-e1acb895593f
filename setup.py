#!/usr/bin/env python3
"""
Setup script for HubScannerV2
Initializes database with required tables and sample scraping links
"""
import mysql.connector
from mysql.connector import <PERSON><PERSON>r
import yaml
import os
from dotenv import load_dotenv

def load_config():
    """Load configuration from YAML file"""
    with open('config/config.yaml', 'r') as file:
        config = yaml.safe_load(file)
    
    # Override with environment variables
    load_dotenv()
    config['mysql']['password'] = os.getenv('MYSQL_PASSWORD', config['mysql']['password'])
    
    return config

def setup_database():
    """Setup database tables and initial data"""
    config = load_config()
    
    try:
        # Connect to database
        connection = mysql.connector.connect(
            host=config['mysql']['host'],
            user=config['mysql']['user'],
            password=config['mysql']['password'],
            database=config['mysql']['database']
        )
        
        if connection.is_connected():
            print("Connected to MySQL database")
            cursor = connection.cursor()
            
            # Read and execute schema
            print("Creating database tables...")
            with open('database/schema.sql', 'r') as schema_file:
                schema_sql = schema_file.read()
                
                # Split by semicolon and execute each statement
                statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
                for statement in statements:
                    cursor.execute(statement)
            
            # Insert default scraping links
            print("Adding default scraping links...")
            scraping_links = [
                ('Indeed', 'https://de.indeed.com/jobs?q=Software+Engineer&l=Berlin&fromage=1', 'Berlin'),
                ('StepStone', 'https://www.stepstone.de/work/software-engineer/in-munich?whereType=autosuggest&radius=30&sort=2&action=sort_publish&ag=age_1', 'Munich'),
                ('Glassdoor', 'https://www.glassdoor.com/Job/munich-bavaria-software-engineer-jobs-SRCH_IL.0,14_IC4990924_KO15,32.htm?fromAge=1', 'Munich')
            ]
            
            for site_name, url, city in scraping_links:
                # Check if link already exists
                cursor.execute("SELECT id FROM scraping_links WHERE url = %s", (url,))
                if not cursor.fetchone():
                    cursor.execute(
                        "INSERT INTO scraping_links (site_name, url, city) VALUES (%s, %s, %s)",
                        (site_name, url, city)
                    )
                    print(f"Added {site_name} scraping link")
                else:
                    print(f"{site_name} scraping link already exists")
            
            connection.commit()
            print("\n✅ Database setup completed successfully!")
            print("\nNext steps:")
            print("1. Copy .env.example to .env and fill in your API keys")
            print("2. Run: python main.py")
            
    except Error as e:
        print(f"❌ Error setting up database: {e}")
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()
            print("MySQL connection closed")

if __name__ == "__main__":
    print("🚀 Setting up HubScannerV2...")
    setup_database()
