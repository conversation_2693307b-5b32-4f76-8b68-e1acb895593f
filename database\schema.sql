CREATE DATABASE IF NOT EXISTS hubscannerv2;

USE hubscannerv2;

CREATE TABLE IF NOT EXISTS scraping_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    url VARCHAR(255) NOT NULL,
    site_name VARCHAR(100) NOT NULL,
    city VARCHAR(100) NOT NULL,
    active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    company VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    city VARCHAR(100) DEFAULT '',
    description TEXT,
    url VARCHAR(1024) NOT NULL,
    posted_date DATE,
    source_site VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_url (url(255)),
    INDEX idx_company (company),
    INDEX idx_city (city),
    INDEX idx_created_at (created_at)
);

-- Insert some example scraping links
INSERT INTO scraping_links (url, site_name, city) VALUES
('https://de.indeed.com/jobs?q=Software+Engineer&l=Berlin&fromage=1', 'Indeed', 'Berlin'),
('https://www.stepstone.de/work/software-engineer/in-munich?whereType=autosuggest&radius=30&sort=2&action=sort_publish&ag=age_1', 'StepStone', 'Munich');

