#!/usr/bin/env python3
"""
Simple duplicate cleaner for HubScannerV2
Removes duplicate jobs based on title, company, and city within a 48-hour window.
Keeps only the first occurrence by created_at timestamp.
"""
import mysql.connector
from mysql.connector import Error
import yaml
import os
from dotenv import load_dotenv

def load_config():
    """Load configuration from YAML file"""
    with open('config/config.yaml', 'r') as file:
        config = yaml.safe_load(file)
    
    # Override with environment variables
    load_dotenv()
    config['mysql']['password'] = os.getenv('MYSQL_PASSWORD', config['mysql']['password'])
    
    return config

def clean_duplicates():
    """Remove duplicate jobs within 48-hour windows"""
    config = load_config()

    try:
        # Connect to database
        connection = mysql.connector.connect(
            host=config['mysql']['host'],
            user=config['mysql']['user'],
            password=config['mysql']['password'],
            database=config['mysql']['database']
        )

        if connection.is_connected():
            print("Connected to MySQL database")
            cursor = connection.cursor()

            # Get total job count before cleaning
            cursor.execute("SELECT COUNT(*) FROM jobs")
            total_before = cursor.fetchone()[0]
            print(f"Total jobs before cleaning: {total_before}")

            # Simple approach: Delete duplicates using SQL window functions
            delete_query = """
            DELETE j1 FROM jobs j1
            INNER JOIN jobs j2
            WHERE j1.id > j2.id
            AND j1.title = j2.title
            AND j1.company = j2.company
            AND j1.city = j2.city
            AND ABS(TIMESTAMPDIFF(HOUR, j1.created_at, j2.created_at)) <= 48
            """

            cursor.execute(delete_query)
            deleted_count = cursor.rowcount
            connection.commit()

            # Get total job count after cleaning
            cursor.execute("SELECT COUNT(*) FROM jobs")
            total_after = cursor.fetchone()[0]

            print(f"\n=== CLEANING RESULTS ===")
            print(f"Jobs before: {total_before}")
            print(f"Jobs after:  {total_after}")
            print(f"Removed:     {deleted_count}")
            print(f"Success! ✅")

            # Return cleanup statistics
            return {
                'before': total_before,
                'after': total_after,
                'removed': deleted_count
            }

    except Error as e:
        print(f"Database error: {e}")
        return {'before': 0, 'after': 0, 'removed': 0}
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()
            print("MySQL connection closed")

if __name__ == "__main__":
    print("🧹 Starting simple duplicate cleaning...")
    clean_duplicates()
    print("✅ Cleaning completed!")
